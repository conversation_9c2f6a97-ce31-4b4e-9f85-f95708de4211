/*
 * Global CSS to completely remove add to cart hover functionality
 * from ALL product listing pages including search results, category pages, etc.
 */

/* Hide product bottom box (contains add to cart) */
.product-inner .product-bottom-box,
.product-inner:hover .product-bottom-box {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
}

/* Hide product loop buttons */
.product-inner .product-loop__buttons,
.product-inner:hover .product-loop__buttons {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
}

/* Prevent hover effects on product cards */
.product-inner {
    overflow: hidden !important;
    position: relative !important;
}

.product-inner:hover {
    border-color: transparent !important;
    position: static !important;
    z-index: 1 !important;
}

/* Hide quantity inputs and add to cart buttons */
.product-inner input[type="number"],
.product-inner .quantity-input,
.product-inner .add-to-cart,
.product-inner .add_to_cart_button,
.product-inner .add-to-cart-button,
.product-inner button[type="submit"],
.product-inner .cart-form,
.product-inner .add_to_cart,
.product-inner .btn-add-to-cart,
.product-inner .btn-add-cart,
.product-inner .btn-cart,
.product-inner .btn-add-to-cart-wrapper,
.product-inner .btn-add-cart-wrapper,
.product-inner .btn-cart-wrapper {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
}

/* Hide any action buttons */
.product-inner .product-actions,
.product-inner .product-footer,
.product-inner .product-button,
.product-inner .product-hover-button,
.product-inner .product-hover-action,
.product-inner .product-hover-actions,
.product-inner .product-action,
.product-inner .product-action-bottom,
.product-inner .product-action-vertical,
.product-inner .hover-buttons,
.product-inner .hover-button,
.product-inner .hover-action,
.product-inner:hover .product-action,
.product-inner:hover .product-actions,
.product-inner:hover .product-button,
.product-inner:hover .product-buttons,
.product-inner:hover .hover-buttons,
.product-inner:hover .hover-action,
.product-inner:hover .hover-actions,
.product-inner:hover .product-hover-action,
.product-inner:hover .product-hover-actions {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
}

/* Hide quantity selector */
.product-inner .quantity,
.product-inner .qty-input,
.product-inner .qty-wrapper,
.product-inner .qty-box,
.product-inner .quantity-input,
.product-inner .quantity-wrapper,
.product-inner .quantity-box,
.product-inner .product-quantity,
.product-inner .product-qty {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
}

/* Hide any cart-related elements */
.product-inner .cart-form,
.product-inner form.cart-form,
.product-inner .sticky-atc-wrap,
.product-inner .cart-wrapper,
.product-inner .cart-box,
.product-inner .cart-container,
.product-inner .cart-buttons,
.product-inner .cart-button,
.product-inner .cart-action {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
}
