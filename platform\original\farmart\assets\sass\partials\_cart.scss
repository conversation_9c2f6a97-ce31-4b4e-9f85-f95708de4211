.cart--mini {
    position: relative;
    display: block;

    .header-item-counter {
        top: 0;
    }

    .cart__content {
        position: absolute;
        min-width: 370px;
        right: -0;
        top: 100%;
        z-index: 30;
        padding-top: 10px;
        @include transform(translate(0, 20px));
        transition: all .4s ease;
        visibility: hidden;
        opacity: 0;
        display: none;
    }

    .cart__footer {
        padding: 10px 20px 20px;
        background-color: #ffffff;
        border: 1px solid #e1e1e1;
        border-top: 0;

        h3 {
            display: block;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;

            strong {
                float: right;
                color: red;
            }
        }
    }

    figure {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        align-items: center;

        .btn {
            padding: 12px 25px;
        }
    }

    .mini-cart-content {
        .widget-shopping-cart-content {
            border: 1px solid #ccc;
            background-color: #fff;
        }

        ul {
            &.mini-product-cart-list {
                border-radius: 3px;
                max-height: 100vh;
                overflow-y: auto;
                overflow-x: hidden;

                &::-webkit-scrollbar {
                    background: 0 0;
                    width: 7px;
                }

                &::-webkit-scrollbar-thumb {
                    background-color: #ccc;
                }

                li {
                    &.mini-cart-item {
                        list-style: none;
                        display: flex;
                        position: relative;
                        padding-bottom: 20px;
                        margin-top: 20px;
                        border-bottom: 1px solid #ccc;

                        &:last-child {
                            border: none;
                        }

                        img {
                            padding: 10px;
                        }
                    }
                }
            }
        }

        .product-content {
            padding-right: 40px;

            .product-image {
                flex: none;
                width: 60px;
                height: 60px;
                margin-right: 20px;
            }

            .product-name {
                margin-bottom: 5px;

                a {
                    line-height: 1.5;
                    color: #000;

                    &:hover {
                        color: #26901b;
                    }
                }
            }
        }

        .quantity {
            .price-amount {
                font-weight: 700;
            }
        }

        .control-buttons {
            padding: 0 25px 30px;
            border-top: 1px solid #e1e1e1;
        }

        .mini-cart__total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 700;
            color: #000;
            padding-top: 10px;

            .price-amount {
                color: $color-primary;
                font-size: 16px;
            }
        }

        .mini-cart__buttons {
            margin: 20px 0 0;

            .btn {
                line-height: 30px;
                font-weight: 700;
                text-align: center;
                padding: 8px 20px;
                color: #222;
                border-radius: 3px;
                border: 1px solid #ccc;
                transition: ease 0.5s;
                width: 100%;
                font-size: 14px;
                white-space: nowrap;

                &.checkout {
                    color: #fff;
                    border-color: transparent;
                }
            }
        }
    }
}

@media (min-width: 1200px) {
    .cart--mini {
        &:hover, &:focus {
            .cart__content {
                @include transform(translate(0, 0px));
                visibility: visible;
                opacity: 1;
            }
        }

        .cart__content {
            display: block;

            .panel__header {
                display: none;
            }
        }

        .cart__items {
            position: relative;

            &:before {
                content: '';
                display: inline-block;
                position: absolute;
                top: -8px;
                right: 30px;
                width: 16px;
                height: 16px;
                border-left: 1px solid #e1e1e1;
                border-top: 1px solid #e1e1e1;
                background-color: #ffffff;
                @include transform(rotate(45deg));
                @include transform-origin(50% 50%);

            }

            .product--cart-mobile {
                margin-bottom: 30px;
            }
        }
    }
}

@media (max-width: 1199px) {
    .cart--mini {
        .cart__content {
            transform: none;
            transition: none;
            right: 0;
            padding-top: 0;
            visibility: visible;
            opacity: 1;
            display: block;

            &.active {
                position: fixed;
                top: 0;
                width: 100%;
                height: 100dvh;
                overflow-y: auto;
                z-index: 10001;
                left: auto;

                .mini-cart-content {
                    transform: translateX(0);
                    visibility: visible;
                    opacity: 1;
                }
            }

            .backdrop {
                position: absolute;
                top: 0;
                bottom: 0;
                height: 100%;
                width: 100%;
                background: #9d97975e;
            }

            .mini-cart-content {
                position: fixed;
                top: 0;
                width: 82%;
                max-width: 400px;
                height: 100dvh;
                overflow-y: auto;
                z-index: 10001;
                background-color: #fff;
                transition: all 0.5s cubic-bezier(0.7, 0, 0.3, 1) 0s;
                left: auto;
                right: 0;
                transform: translateX(100%);
                padding-top: 0;

                .widget-shopping-cart-content {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    height: 100%;

                    .cart__items {
                        height: 100%;
                        overflow-y: auto;
                    }
                }
            }
        }
    }
}

.cart-form__contents {
    > :not(:first-child) {
        border-top-width: 0;
    }

    tbody {
        td {
            vertical-align: top;
            padding: 20px 10px;

            &.product-thumbnail {
                width: 90px;
            }

            .product-button .quantity {
                margin-bottom: 0px;
            }

            &.product-quantity {
                min-width: 120px;
            }
        }
    }

    thead {
        th {
            background-color: #f5f5f5;
            font-weight: 400;
            color: #555;
            padding: 11px 10px;
        }

        tr {
            border-top: 1px solid #eeeeee;
        }
    }

    tbody, thead {
        tr {
            border-bottom: 1px solid #eeeeee;
        }

        td, th {
            border-bottom-width: 0;

            &:first-child {
                border-left-width: 1px;
            }

            &:last-child {
                border-right-width: 1px;
            }
        }
    }
}

@media (max-width: 992px) {
    .cart-form__contents {
        tbody {
            td {
                padding: 10px 10px;
            }
        }
    }
}

@media (max-width: 767px) {
    .cart-form__contents {
        tbody {
            td {
                padding: 10px 7px;

                &.product-md {
                    border-top-width: 0;
                    padding-top: 0px;
                }

                &.product-subtotal {
                    .box-price {
                        .amount {
                            color: $color-green;
                        }
                    }
                }
            }
        }
    }
}

.wishlist-page-content {
    .cart-form__contents {
        .cart_item {
            .product-button {
                .quantity {
                    display: none;
                }
            }
        }
    }
}
