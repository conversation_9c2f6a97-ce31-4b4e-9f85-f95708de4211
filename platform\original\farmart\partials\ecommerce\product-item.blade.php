@php
    $showPrice = EcommerceHelper::isEnabledCustomerRecentlyViewedProducts();
    $hasVariation = $product->variations()->count() > 0;
@endphp

<div class="product-inner bg-white">
    <div class="product-thumbnail">
        <a class="product-loop__link" href="{{ $product->url }}">
            <img class="product-thumbnail__img"
                src="{{ RvMedia::getImageUrl($product->image, 'medium', false, RvMedia::getDefaultImage()) }}"
                alt="{{ $product->name }}"
                loading="lazy">
                
            @if ($product->isOutOfStock())
                <span class="ribbon out-stock">{{ __('Out Of Stock') }}</span>
            @else
                @if ($product->productLabels->isNotEmpty())
                    @foreach ($product->productLabels as $label)
                        <span class="ribbon" @if ($label->color) style="background-color: {{ $label->color }}" @endif>{{ $label->name }}</span>
                    @endforeach
                @else
                    @if ($product->front_sale_price !== $product->price)
                        <div class="ribbon sale">-{{ get_sale_percentage($product->price, $product->front_sale_price) }}</div>
                    @endif
                @endif
            @endif
        </a>
    </div>

    <div class="product-details">
        @if (is_plugin_active('marketplace') && $product->store->id)
            <div class="sold-by-meta">
                <a href="{{ $product->store->url }}" class="store-link">{{ $product->store->name }}</a>
            </div>
        @endif

        <h3 class="product__title">
            <a href="{{ $product->url }}">{{ $product->name }}</a>
        </h3>

        @if (EcommerceHelper::isReviewEnabled())
            {!! Theme::partial('star-rating', ['avg' => $product->reviews_avg, 'count' => $product->reviews_count]) !!}
        @endif

        <div class="product-price-wrapper">
            {!! Theme::partial('ecommerce.product-price', compact('product')) !!}
        </div>

        @if (!empty($isFlashSale))
            <div class="deal-sold">
                @if (Botble\Ecommerce\Facades\FlashSale::isShowSaleCountLeft())
                    <div class="deal-text">
                        <span class="sold fw-bold">
                            @if ($product->pivot->quantity > $product->pivot->sold)
                                <span class="text">{{ __('Sold') }}: </span>
                                <span class="value">{{ (int) $product->pivot->sold }} / {{ (int) $product->pivot->quantity }}</span>
                            @else
                                <span class="text text-danger">{{ __('Sold out') }}</span>
                            @endif
                        </span>
                    </div>
                @endif
                <div class="deal-progress">
                    <div class="progress">
                        <div class="progress-bar"
                            role="progressbar"
                            aria-valuenow="{{ $product->pivot->quantity > 0 ? ($product->pivot->sold / $product->pivot->quantity) * 100 : 0 }}"
                            aria-valuemin="0"
                            aria-valuemax="100"
                            style="width: {{ $product->pivot->quantity > 0 ? ($product->pivot->sold / $product->pivot->quantity) * 100 : 0 }}%">
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <div class="product-bottom-box">
            <div class="quantity-box">
                <div class="quantity buttons-added">
                    <button type="button" class="minus">-</button>
                    <input type="number" name="qty" value="1" title="Qty" class="qty input-text qty-text" size="4" min="1" inputmode="numeric">
                    <button type="button" class="plus">+</button>
                </div>
                <button type="button" class="btn-add-cart" data-id="{{ $product->id }}" data-url="{{ route('public.cart.add-to-cart') }}">
                    <i class="far fa-shopping-cart"></i>
                    {{ __('Add to cart') }}
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.product-inner {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    border: 1px solid #eee;
}

.product-inner:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.product-thumbnail {
    position: relative;
    padding-top: 100%;
    overflow: hidden;
}

.product-loop__link {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.product-thumbnail__img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 10px;
    background: #fff;
}

.ribbon {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    background: #ff6633;
    color: #fff;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

.ribbon.sale {
    background: #ff6633;
}

.ribbon.out-stock {
    background: #999;
}

.product-details {
    padding: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.sold-by-meta {
    margin-bottom: 10px;
}

.store-link {
    color: #000080;
    font-size: 12px;
    text-decoration: none;
    padding: 2px 8px;
    border: 1px solid #000080;
    border-radius: 3px;
    transition: all 0.3s ease;
    display: inline-block;
}

.store-link:hover {
    color: #ff6633;
    border-color: #ff6633;
}

.product__title {
    margin: 0 0 10px;
    font-size: 16px;
    line-height: 1.4;
}

.product__title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.product__title a:hover {
    color: #ff6633;
}

.product-price-wrapper {
    margin: 10px 0;
    font-size: 16px;
    font-weight: 600;
}

.deal-sold {
    margin-top: 10px;
}

.progress {
    height: 8px;
    background: #eee;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 5px;
}

.progress-bar {
    background: #ff6633;
}

.quantity-box {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
}

.quantity.buttons-added {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.quantity.buttons-added .minus,
.quantity.buttons-added .plus {
    background: none;
    border: none;
    padding: 5px 10px;
    cursor: pointer;
    color: #666;
    font-size: 16px;
}

.quantity.buttons-added .qty {
    width: 50px;
    text-align: center;
    border: none;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    padding: 5px;
    margin: 0;
}

.btn-add-cart {
    flex-grow: 1;
    background: #ff6633;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
    font-weight: 500;
}

.btn-add-cart:hover {
    background: #ff5522;
}

.btn-add-cart i {
    font-size: 16px;
}

/* Hide number input spinners */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Mobile Responsive */
@media (max-width: 991px) {
    .product-inner .product-details {
        text-align: left !important;
    }

    .product-inner .product-details .sold-by-meta {
        text-align: left !important;
        display: block !important;
    }

    .product-inner .product-details .product__title {
        text-align: left !important;
    }

    .product-inner .product-details .star-rating-wrapper {
        justify-content: flex-start !important;
        display: flex !important;
    }

    .product-inner .product-details .product-price-wrapper {
        text-align: left !important;
    }

    .product-inner .product-details > * {
        justify-content: flex-start !important;
        align-items: flex-start !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quantityBoxes = document.querySelectorAll('.quantity.buttons-added');
    
    quantityBoxes.forEach(box => {
        const minusBtn = box.querySelector('.minus');
        const plusBtn = box.querySelector('.plus');
        const qtyInput = box.querySelector('.qty');
        
        minusBtn.addEventListener('click', () => {
            let value = parseInt(qtyInput.value);
            if (value > 1) {
                qtyInput.value = value - 1;
            }
        });
        
        plusBtn.addEventListener('click', () => {
            let value = parseInt(qtyInput.value);
            qtyInput.value = value + 1;
        });
        
        qtyInput.addEventListener('change', () => {
            let value = parseInt(qtyInput.value);
            if (value < 1 || isNaN(value)) {
                qtyInput.value = 1;
            }
        });
    });
});
</script>
