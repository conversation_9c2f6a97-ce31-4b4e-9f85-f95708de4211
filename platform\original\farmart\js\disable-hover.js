/**
 * This script injects CSS to disable add to cart hover functionality
 * It runs immediately and with the highest priority
 */

(function() {
    // Function to inject CSS with highest priority
    function injectCSS() {
        // Create a style element
        const style = document.createElement('style');
        style.id = 'disable-hover-css';
        style.setAttribute('data-priority', 'highest');
        
        // Set the CSS content
        style.textContent = `
            /* HIDE ALL PRODUCT BOTTOM BOXES - CONTAINS ADD TO CART */
            .product-inner .product-bottom-box,
            .products .product-inner .product-bottom-box,
            .product-inner:hover .product-bottom-box,
            .products .product-inner:hover .product-bottom-box,
            .product-bottom-box,
            *[class*="product"] .product-bottom-box,
            *[class*="product"]:hover .product-bottom-box {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
                pointer-events: none !important;
                width: 0 !important;
                height: 0 !important;
                position: absolute !important;
                left: -9999px !important;
                z-index: -999 !important;
                max-height: 0 !important;
                overflow: hidden !important;
                clip: rect(0, 0, 0, 0) !important;
                margin: 0 !important;
                padding: 0 !important;
                border: 0 !important;
            }
            
            /* HIDE ALL PRODUCT LOOP BUTTONS */
            .product-inner .product-loop__buttons,
            .products .product-inner .product-loop__buttons,
            .product-inner:hover .product-loop__buttons,
            .products .product-inner:hover .product-loop__buttons,
            .product-loop__buttons,
            *[class*="product"] .product-loop__buttons,
            *[class*="product"]:hover .product-loop__buttons {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
                pointer-events: none !important;
                width: 0 !important;
                height: 0 !important;
                position: absolute !important;
                left: -9999px !important;
                z-index: -999 !important;
                max-height: 0 !important;
                overflow: hidden !important;
                clip: rect(0, 0, 0, 0) !important;
                margin: 0 !important;
                padding: 0 !important;
                border: 0 !important;
            }
        `;
        
        // Insert at the beginning of the head for highest priority
        if (document.head.firstChild) {
            document.head.insertBefore(style, document.head.firstChild);
        } else {
            document.head.appendChild(style);
        }
        
        // Also add the external CSS file
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = '/themes/farmart/css/disable-hover.css';
        
        // Insert after our inline style
        if (document.head.firstChild) {
            document.head.insertBefore(link, document.head.firstChild.nextSibling);
        } else {
            document.head.appendChild(link);
        }
    }
    
    // Function to remove hover event listeners
    function removeHoverListeners() {
        // Get all product cards
        const productCards = document.querySelectorAll('.product-inner, .products .product-inner, [class*="product"]');
        
        // Loop through each card
        productCards.forEach(function(card) {
            if (card) {
                // Set overflow to hidden to prevent hover elements from showing
                card.style.overflow = 'hidden';
                
                // Remove any existing hover event listeners (using cloneNode trick)
                const newCard = card.cloneNode(true);
                if (card.parentNode) {
                    card.parentNode.replaceChild(newCard, card);
                }
                
                // Add a mouseenter event listener that prevents default behavior
                newCard.addEventListener('mouseenter', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    
                    // Hide any cart elements that might appear on hover
                    const elements = this.querySelectorAll('.product-bottom-box, .product-loop__buttons, .product-action, .product-actions, .product-button, .product-buttons, .hover-buttons, .hover-action, .hover-actions, .product-hover-action, .product-hover-actions');
                    
                    elements.forEach(function(element) {
                        element.style.display = 'none';
                        element.style.opacity = '0';
                        element.style.visibility = 'hidden';
                        element.style.pointerEvents = 'none';
                    });
                    
                    return false;
                }, true);
            }
        });
    }
    
    // Function to set up a MutationObserver
    function setupMutationObserver() {
        // Create a new MutationObserver
        const observer = new MutationObserver(function(mutations) {
            // Apply our fixes
            removeHoverListeners();
            
            // Hide elements directly
            const elements = document.querySelectorAll('.product-bottom-box, .product-loop__buttons, .product-action, .product-actions, .product-button, .product-buttons, .hover-buttons, .hover-action, .hover-actions, .product-hover-action, .product-hover-actions');
            
            elements.forEach(function(element) {
                element.style.display = 'none';
                element.style.opacity = '0';
                element.style.visibility = 'hidden';
                element.style.pointerEvents = 'none';
            });
        });
        
        // Start observing the document
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            characterData: true
        });
    }
    
    // Run immediately
    injectCSS();
    
    // Run when the DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            removeHoverListeners();
            setupMutationObserver();
        });
    } else {
        removeHoverListeners();
        setupMutationObserver();
    }
    
    // Also run on window load
    window.addEventListener('load', function() {
        removeHoverListeners();
    });
    
    // Run periodically to catch any dynamically loaded content
    setInterval(removeHoverListeners, 1000);
})();
