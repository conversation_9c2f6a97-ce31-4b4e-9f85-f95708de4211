<span class="product-price">
    @if ($product->front_sale_price !== $product->price)
        <span class="product-price-sale d-flex align-items-center bb-product-price">
            <del aria-hidden="true">
                <span class="price-amount">
                    <bdi>
                        <span class="amount bb-product-price-text-old" data-bb-value="product-original-price">{{ format_price($product->price_with_taxes) }}</span>
                    </bdi>
                </span>
            </del>
            <ins>
                <span class="price-amount">
                    <bdi>
                        <span class="amount bb-product-price-text" data-bb-value="product-price">{{ format_price($product->front_sale_price_with_taxes) }}</span>
                    </bdi>
                </span>
            </ins>
        </span>
    @else
        <span class="product-price-original">
            <span class="price-amount">
                <bdi>
                    <span class="amount bb-product-price-text" data-bb-value="product-price">{{ format_price($product->front_sale_price_with_taxes) }}</span>
                </bdi>
            </span>
        </span>
    @endif
</span>
