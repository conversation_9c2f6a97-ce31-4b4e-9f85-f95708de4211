// Fix for modal close button not working
document.addEventListener('DOMContentLoaded', function() {
    // Add a custom event handler for the close button in the product quick view modal
    document.addEventListener('ecommerce.quick-view.initialized', function() {
        // Get the modal element
        const modal = document.getElementById('product-quick-view-modal');
        if (!modal) return;
        
        // Get the close button
        const closeButton = modal.querySelector('.btn-close');
        if (!closeButton) return;
        
        // Remove existing event listeners and add our own
        closeButton.replaceWith(closeButton.cloneNode(true));
        
        // Get the new close button (after replacement)
        const newCloseButton = modal.querySelector('.btn-close');
        
        // Add our custom event listener
        newCloseButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Close the modal using Bootstrap's API
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            } else {
                // Fallback if bootstrap instance not found
                $(modal).modal('hide');
            }
        });
        
        // Increase the z-index of the close button
        newCloseButton.style.zIndex = '10001';
        newCloseButton.style.position = 'absolute';
        newCloseButton.style.top = '20px';
        newCloseButton.style.right = '20px';
        newCloseButton.style.cursor = 'pointer';
        
        console.log('Modal close button enhanced');
    });
    
    // Also add a click handler to the modal backdrop to close the modal
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-backdrop')) {
            const modal = document.getElementById('product-quick-view-modal');
            if (modal) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                } else {
                    $(modal).modal('hide');
                }
            }
        }
    });
});
