/* Grid layout for search results */
.more-to-love-products-grid {
    display: grid !important;
    grid-template-columns: repeat(6, 1fr) !important;
    gap: 1rem !important;
}

@media (max-width: 1400px) {
    .more-to-love-products-grid {
        grid-template-columns: repeat(5, 1fr) !important;
    }
}

@media (max-width: 1199px) {
    .more-to-love-products-grid {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}

@media (max-width: 1024px) {
    .more-to-love-products-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

@media (max-width: 767px) {
    .more-to-love-products-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 0.5rem !important;
    }
}

/* Hide any other product listings */
.shop-products-listing {
    display: none !important;
}

/* Hide view options section */
body.page-search .catalog-toolbar__view,
body.page-search .toolbar-view__icon,
body.page-search .catalog-header__right {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* Fix double price display in 3-column grid */
body.page-search .product-inner .d-none.d-md-block:not(.flash-sale-price) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    pointer-events: none !important;
}

/* Style for flash sale price */
body.page-search .product-inner .flash-sale-price {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    width: auto !important;
    margin: 10px 0 !important;
    padding: 0 !important;
    overflow: visible !important;
    position: relative !important;
    pointer-events: auto !important;
}

/* Reset any existing alignment styles */
.product-inner,
.product-inner *,
.product-details,
.product-details * {
    text-align: left !important;
}

/* Remove gap between image and price tag */
.product-inner .product-thumbnail {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

.product-inner .product-thumbnail a,
.product-inner .product-thumbnail img {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
    display: block !important;
}

.product-inner .product-details {
    margin-top: -5px !important;
    padding-top: 0 !important;
}

/* Target specific elements */
.product-inner .product-details .product-content-box {
    padding: 15px !important;
    text-align: left !important;
}

.product-inner .product-details .product-content-box .sold-by-meta {
    text-align: left !important;
    margin-bottom: 5px !important;
}

.product-inner .product-details .product-content-box .sold-by-meta a {
    text-align: left !important;
    display: inline-block !important;
}

/* Hide quantity selector and add to cart button on search results page */
body.page-search .quantity,
body.page-search .add-to-cart-button,
body.page-search .product-cart-wrap,
body.page-search .product-button,
body.page-search .product-bottom-button,
body.page-search .product-cart-wrap,
body.page-search .product-hover-button,
body.page-search .product-hover-action,
body.page-search .product-hover-actions,
body.page-search .product-action,
body.page-search .product-actions,
body.page-search .product-action-bottom,
body.page-search .product-action-vertical,
body.page-search .hover-buttons,
body.page-search .hover-button,
body.page-search .hover-action {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Hide any action buttons on search results */
body.page-search .product-inner .product-actions,
body.page-search .product-inner .product-footer,
body.page-search .product-inner .product-button,
body.page-search .product-inner .product-hover-button,
body.page-search .product-inner .product-hover-action,
body.page-search .product-inner .product-hover-actions,
body.page-search .product-inner .product-action,
body.page-search .product-inner .product-action-bottom,
body.page-search .product-inner .product-action-vertical,
body.page-search .product-inner .hover-buttons,
body.page-search .product-inner .hover-button,
body.page-search .product-inner .hover-action {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Force hide any cart buttons */
body.page-search .add_to_cart_button,
body.page-search .button.add-to-cart-button,
body.page-search button[type="submit"],
body.page-search .cart-form,
body.page-search .add-to-cart,
body.page-search .add_to_cart,
body.page-search .btn-add-to-cart,
body.page-search .btn-add-cart,
body.page-search .btn-cart,
body.page-search .btn-add-to-cart-wrapper,
body.page-search .btn-add-cart-wrapper,
body.page-search .btn-cart-wrapper {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Hide quantity selector */
body.page-search .quantity,
body.page-search .qty-input,
body.page-search .qty-wrapper,
body.page-search .qty-box,
body.page-search .quantity-input,
body.page-search .quantity-wrapper,
body.page-search .quantity-box,
body.page-search .product-quantity,
body.page-search .product-qty {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Hide any cart-related elements */
body.page-search .cart-form,
body.page-search form.cart-form,
body.page-search .sticky-atc-wrap,
body.page-search .cart-wrapper,
body.page-search .cart-box,
body.page-search .cart-container,
body.page-search .cart-buttons,
body.page-search .cart-button,
body.page-search .cart-action {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Specifically target hover elements */
body.page-search .product-inner:hover .product-action,
body.page-search .product-inner:hover .product-actions,
body.page-search .product-inner:hover .product-button,
body.page-search .product-inner:hover .product-buttons,
body.page-search .product-inner:hover .hover-buttons,
body.page-search .product-inner:hover .hover-action,
body.page-search .product-inner:hover .hover-actions,
body.page-search .product-inner:hover .product-hover-action,
body.page-search .product-inner:hover .product-hover-actions {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}
