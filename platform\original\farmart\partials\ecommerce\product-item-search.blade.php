@php
    $isOutOfStock = $product->isOutOfStock();
@endphp

<div class="product-thumbnail">
    <a href="{{ $product->url }}">
        <img src="{{ RvMedia::getImageUrl($product->image, 'medium', false, RvMedia::getDefaultImage()) }}" 
             alt="{{ $product->name }}" 
             class="product-thumbnail__img">
    </a>
    @if ($isOutOfStock)
        <div class="product-badge out-of-stock">{{ __('Out Of Stock') }}</div>
    @else
        @if ($product->productLabels->count())
            @foreach ($product->productLabels as $label)
                <div class="product-badge" style="background-color: {{ $label->color }}">{{ $label->name }}</div>
            @endforeach
        @else
            @if ($product->front_sale_price !== $product->price)
                <div class="product-badge sale">{{ get_sale_percentage($product->price, $product->front_sale_price) }}</div>
            @endif
        @endif
    @endif
    
    <div class="wishlist-button">
        <a class="wishlist add-to-wishlist" href="{{ route('public.wishlist.add', $product->id) }}" 
           data-url="{{ route('public.wishlist.add', $product->id) }}"
           title="{{ __('Add to wishlist') }}">
            <i class="far fa-heart"></i>
        </a>
    </div>
</div>
<div class="product-details">
    <div class="product-content-box">
        @if (is_plugin_active('marketplace') && $product->store->id)
            <div class="sold-by-meta">
                <a href="{{ $product->store->url }}">{{ $product->store->name }}</a>
            </div>
        @endif
        <h3 class="product__title">
            <a href="{{ $product->url }}">{{ $product->name }}</a>
        </h3>
        @if (EcommerceHelper::isReviewEnabled())
            <div class="rating_wrap">
                <div class="rating">
                    <div class="product_rate" style="width: {{ $product->reviews_avg * 20 }}%"></div>
                </div>
                <span class="rating_num">({{ $product->reviews_count }})</span>
            </div>
        @endif
        <div class="product-price">
            <span class="price">{{ format_price($product->front_sale_price_with_taxes) }}</span>
            @if ($product->front_sale_price !== $product->price)
                <del>{{ format_price($product->price_with_taxes) }}</del>
            @endif
        </div>
    </div>
</div>
