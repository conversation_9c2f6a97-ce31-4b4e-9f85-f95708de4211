// Custom script to move product hover buttons to the right
document.addEventListener('DOMContentLoaded', function() {
    // Apply styles directly to elements
    document.querySelectorAll(".product-loop__buttons").forEach(function(el) {
        el.style.right = "-15px";
        el.style.position = "absolute";
        el.style.top = "0";
        el.style.zIndex = "10";
    });
    
    // Add hover effect
    document.querySelectorAll(".product-inner").forEach(function(product) {
        product.style.overflow = "visible";
        
        product.addEventListener("mouseenter", function() {
            const buttons = this.querySelectorAll(".product-loop__buttons .product-loop_action");
            buttons.forEach(function(btn) {
                btn.style.transform = "translateX(0)";
            });
        });
        
        product.addEventListener("mouseleave", function() {
            const buttons = this.querySelectorAll(".product-loop__buttons .product-loop_action");
            buttons.forEach(function(btn) {
                btn.style.transform = "translateX(20px)";
            });
        });
    });
    
    console.log('Custom hover buttons script loaded');
});
