/**
 * Global script to completely remove add to cart hover functionality
 * from ALL product listing pages including search results, category pages, etc.
 */

(function() {
    // Function to remove add to cart hover functionality
    function removeAddToCartHover() {
        // Remove hover event listeners from product cards
        const productCards = document.querySelectorAll('.product-inner');
        productCards.forEach(function(card) {
            if (card) {
                // Set overflow to hidden to prevent hover elements from showing
                card.style.overflow = 'hidden';

                // Remove any existing hover event listeners (using cloneNode trick)
                const newCard = card.cloneNode(true);
                if (card.parentNode) {
                    card.parentNode.replaceChild(newCard, card);
                }

                // Prevent hover effects
                newCard.addEventListener('mouseenter', function(e) {
                    // Hide any cart elements that might appear on hover
                    const bottomBox = this.querySelector('.product-bottom-box');
                    if (bottomBox) {
                        bottomBox.style.display = 'none';
                        bottomBox.style.opacity = '0';
                        bottomBox.style.visibility = 'hidden';
                        bottomBox.style.pointerEvents = 'none';
                    }

                    const loopButtons = this.querySelector('.product-loop__buttons');
                    if (loopButtons) {
                        loopButtons.style.display = 'none';
                        loopButtons.style.opacity = '0';
                        loopButtons.style.visibility = 'hidden';
                        loopButtons.style.pointerEvents = 'none';
                    }
                });
            }
        });

        // Hide product bottom box (contains add to cart)
        const productBottomBoxes = document.querySelectorAll('.product-bottom-box');
        productBottomBoxes.forEach(function(box) {
            if (box) {
                box.style.display = 'none';
                box.style.opacity = '0';
                box.style.visibility = 'hidden';
                box.style.pointerEvents = 'none';

                // Also hide it using !important inline style
                box.setAttribute('style', 'display: none !important; opacity: 0 !important; visibility: hidden !important; pointer-events: none !important; width: 0 !important; height: 0 !important; position: absolute !important; left: -9999px !important; z-index: -999 !important;');
            }
        });

        // Hide product loop buttons
        const productLoopButtons = document.querySelectorAll('.product-loop__buttons');
        productLoopButtons.forEach(function(buttons) {
            if (buttons) {
                buttons.style.display = 'none';
                buttons.style.opacity = '0';
                buttons.style.visibility = 'hidden';
                buttons.style.pointerEvents = 'none';

                // Also hide it using !important inline style
                buttons.setAttribute('style', 'display: none !important; opacity: 0 !important; visibility: hidden !important; pointer-events: none !important; width: 0 !important; height: 0 !important; position: absolute !important; left: -9999px !important; z-index: -999 !important;');
            }
        });

        // Hide quantity selectors
        const quantitySelectors = document.querySelectorAll('.quantity, .qty-input, .qty-wrapper, .qty-box, .quantity-input, .quantity-wrapper, .quantity-box, .product-quantity, .product-qty');
        quantitySelectors.forEach(function(element) {
            if (element) {
                element.style.display = 'none';
                element.style.opacity = '0';
                element.style.visibility = 'hidden';
                element.style.pointerEvents = 'none';

                // Also hide it using !important inline style
                element.setAttribute('style', 'display: none !important; opacity: 0 !important; visibility: hidden !important; pointer-events: none !important; width: 0 !important; height: 0 !important; position: absolute !important; left: -9999px !important; z-index: -999 !important;');
            }
        });

        // Remove add to cart buttons
        const cartButtons = document.querySelectorAll('.add-to-cart-button, .product-cart-wrap, .product-button, button[type="submit"], .cart-form, .add-to-cart, .add_to_cart, .btn-add-to-cart, .btn-add-cart, .btn-cart, .btn-add-to-cart-wrapper, .btn-add-cart-wrapper, .btn-cart-wrapper');
        cartButtons.forEach(function(element) {
            if (element) {
                element.style.display = 'none';
                element.style.opacity = '0';
                element.style.visibility = 'hidden';
                element.style.pointerEvents = 'none';

                // Also hide it using !important inline style
                element.setAttribute('style', 'display: none !important; opacity: 0 !important; visibility: hidden !important; pointer-events: none !important; width: 0 !important; height: 0 !important; position: absolute !important; left: -9999px !important; z-index: -999 !important;');
            }
        });

        // Remove product action buttons and hover elements
        const actionButtons = document.querySelectorAll('.product-actions, .product-footer, .product-button, .product-hover-button, .product-hover-action, .product-hover-actions, .product-action, .product-action-bottom, .product-action-vertical, .hover-buttons, .hover-button, .hover-action');
        actionButtons.forEach(function(element) {
            if (element) {
                element.style.display = 'none';
                element.style.opacity = '0';
                element.style.visibility = 'hidden';
                element.style.pointerEvents = 'none';

                // Also hide it using !important inline style
                element.setAttribute('style', 'display: none !important; opacity: 0 !important; visibility: hidden !important; pointer-events: none !important; width: 0 !important; height: 0 !important; position: absolute !important; left: -9999px !important; z-index: -999 !important;');
            }
        });
    }

    // Function to inject CSS
    function injectCSS() {
        // Create a link element
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = '/themes/farmart/css/remove-cart-hover.css';

        // Append to the head
        document.head.appendChild(link);

        // Also inject inline CSS for immediate effect
        const style = document.createElement('style');
        style.textContent = `
            /* Hide product bottom box and loop buttons */
            .product-inner .product-bottom-box,
            .product-inner:hover .product-bottom-box,
            .product-inner .product-loop__buttons,
            .product-inner:hover .product-loop__buttons {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
                pointer-events: none !important;
                width: 0 !important;
                height: 0 !important;
                position: absolute !important;
                left: -9999px !important;
                z-index: -999 !important;
            }

            /* Prevent hover effects on product cards */
            .product-inner {
                overflow: hidden !important;
                position: relative !important;
            }

            .product-inner:hover {
                border-color: transparent !important;
                position: static !important;
                z-index: 1 !important;
            }

            /* Hide quantity inputs and add to cart buttons */
            .product-inner input[type="number"],
            .product-inner .quantity-input,
            .product-inner .add-to-cart,
            .product-inner .add_to_cart_button,
            .product-inner .add-to-cart-button,
            .product-inner button[type="submit"],
            .product-inner .cart-form,
            .product-inner .add_to_cart,
            .product-inner .btn-add-to-cart,
            .product-inner .btn-add-cart,
            .product-inner .btn-cart,
            .product-inner .btn-add-to-cart-wrapper,
            .product-inner .btn-add-cart-wrapper,
            .product-inner .btn-cart-wrapper {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
                pointer-events: none !important;
                width: 0 !important;
                height: 0 !important;
                position: absolute !important;
                left: -9999px !important;
                z-index: -999 !important;
            }

            /* Hide any action buttons */
            .product-inner .product-actions,
            .product-inner .product-footer,
            .product-inner .product-button,
            .product-inner .product-hover-button,
            .product-inner .product-hover-action,
            .product-inner .product-hover-actions,
            .product-inner .product-action,
            .product-inner .product-action-bottom,
            .product-inner .product-action-vertical,
            .product-inner .hover-buttons,
            .product-inner .hover-button,
            .product-inner .hover-action,
            .product-inner:hover .product-action,
            .product-inner:hover .product-actions,
            .product-inner:hover .product-button,
            .product-inner:hover .product-buttons,
            .product-inner:hover .hover-buttons,
            .product-inner:hover .hover-action,
            .product-inner:hover .hover-actions,
            .product-inner:hover .product-hover-action,
            .product-inner:hover .product-hover-actions {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
                pointer-events: none !important;
                width: 0 !important;
                height: 0 !important;
                position: absolute !important;
                left: -9999px !important;
                z-index: -999 !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Function to handle AJAX pagination and any page navigation
    function setupPaginationHandler() {
        // Add event listener for ALL clicks on the document
        document.addEventListener('click', function(e) {
            // Apply fixes immediately for any click
            removeAddToCartHover();

            // Apply fixes after a delay to catch any dynamically loaded content
            setTimeout(removeAddToCartHover, 500);
            setTimeout(removeAddToCartHover, 1000);
            setTimeout(removeAddToCartHover, 2000);
        });

        // Set up AJAX complete handler for ALL AJAX requests
        if (typeof jQuery !== 'undefined') {
            jQuery(document).ajaxComplete(function() {
                // Apply fixes immediately
                removeAddToCartHover();

                // Apply fixes after a delay to catch any dynamically loaded content
                setTimeout(removeAddToCartHover, 500);
                setTimeout(removeAddToCartHover, 1000);
                setTimeout(removeAddToCartHover, 2000);
            });

            // Also handle jQuery page load events
            jQuery(document).ready(function() {
                removeAddToCartHover();
                setTimeout(removeAddToCartHover, 500);
                setTimeout(removeAddToCartHover, 1000);
                setTimeout(removeAddToCartHover, 2000);
            });
        }

        // Handle URL changes (for SPA-like behavior)
        window.addEventListener('popstate', function() {
            removeAddToCartHover();
            setTimeout(removeAddToCartHover, 500);
            setTimeout(removeAddToCartHover, 1000);
            setTimeout(removeAddToCartHover, 2000);
        });
    }

    // Set up a MutationObserver to watch for dynamically added content
    function setupMutationObserver() {
        const observer = new MutationObserver(function(mutations) {
            // Apply fixes immediately
            removeAddToCartHover();

            // Check if any mutations added product elements
            let hasProductElements = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // Element node
                            if (node.classList && (
                                node.classList.contains('product-inner') ||
                                node.querySelector('.product-inner') ||
                                node.classList.contains('product-bottom-box') ||
                                node.querySelector('.product-bottom-box') ||
                                node.classList.contains('product-loop__buttons') ||
                                node.querySelector('.product-loop__buttons')
                            )) {
                                hasProductElements = true;
                                break;
                            }
                        }
                    }
                }
            });

            // If product elements were added, apply fixes again after a delay
            if (hasProductElements) {
                setTimeout(removeAddToCartHover, 100);
                setTimeout(removeAddToCartHover, 500);
                setTimeout(removeAddToCartHover, 1000);
            }
        });

        // Start observing the document with all possible options
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            characterData: true,
            attributeOldValue: true,
            characterDataOldValue: true
        });
    }

    // Run when the DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Inject CSS
        injectCSS();

        // Apply fixes immediately
        removeAddToCartHover();

        // Set up pagination handler
        setupPaginationHandler();

        // Set up mutation observer
        setupMutationObserver();

        // Apply fixes after a delay to catch any dynamically loaded content
        setTimeout(removeAddToCartHover, 500);
        setTimeout(removeAddToCartHover, 1000);
        setTimeout(removeAddToCartHover, 2000);
    });

    // Also run immediately in case the DOM is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        // Inject CSS
        injectCSS();

        // Apply fixes immediately
        removeAddToCartHover();
    }
})();
