/**
 * Mobile spacing fix to prevent content from being hidden by sticky bar
 */

/* Add extra space at the bottom of all pages in mobile view */
@media (max-width: 991px) {
    /* Add padding to the bottom of the main content */
    body {
        padding-bottom: 120px !important;
    }
    
    /* Add margin to the footer */
    footer.footer {
        margin-top: 100px !important;
    }
    
    /* Add a spacer element at the end of the content */
    #main-content::after {
        content: '';
        display: block;
        height: 120px;
        width: 100%;
        clear: both;
    }
    
    /* Add padding to the last container */
    .container-xxxl:last-child,
    .container-fluid:last-child,
    #main-content > div:last-child,
    #main-content > section:last-child {
        padding-bottom: 120px !important;
    }
    
    /* Add margin to the pagination container */
    .pagination-container {
        margin-bottom: 120px !important;
    }
    
    /* Add margin to the product grid */
    .products-listing {
        margin-bottom: 120px !important;
    }
    
    /* Add margin to the search results */
    .search-result-container {
        margin-bottom: 120px !important;
    }
    
    /* Add margin to any last element that might be close to the bottom */
    .product-inner:last-child,
    .col-12:last-child,
    .col-md-4:last-child,
    .col-lg-4:last-child,
    .col-6:last-child,
    .col-sm-6:last-child {
        margin-bottom: 120px !important;
    }
}
