<!DOCTYPE html>
<html {!! Theme::htmlAttributes() !!}>
<head>
    <meta charset="utf-8">
    <meta
        http-equiv="X-UA-Compatible"
        content="IE=edge"
    >
    <meta
        name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5, user-scalable=1"
    />
    <meta
        name="csrf-token"
        content="{{ csrf_token() }}"
    >


    <!-- Inline script to immediately remove specific Wishlist and Compare buttons -->
    <script>
        // Immediately executing function to remove only specific Wishlist and Compare buttons
        (function() {
            // Function to run immediately
            function removeSpecificButtons() {
                // Only target buttons in the product detail page, not in the header
                var productPage = document.querySelector('.product-detail-container');
                if (productPage) {
                    // Find all Wishlist and Compare links in the product page
                    var productLinks = productPage.querySelectorAll('a');
                    for (var i = 0; i < productLinks.length; i++) {
                        var link = productLinks[i];
                        var text = link.textContent.trim();

                        if (text === 'Wishlist' || text === 'Compare') {
                            // Hide the link
                            link.style.display = 'none';

                            // Also hide parent elements
                            var parent = link.parentElement;
                            if (parent) {
                                parent.style.display = 'none';
                            }
                        }
                    }
                }
            }

            // Run immediately
            removeSpecificButtons();

            // Also run when DOM is loaded
            document.addEventListener('DOMContentLoaded', removeSpecificButtons);

            // Run periodically to catch any dynamically added elements
            setInterval(removeSpecificButtons, 100);
        })();
    </script>

    <!-- Inline style to hide only specific Wishlist and Compare buttons -->
    <style>
        /* Only target buttons in the product detail page, not in the header */
        .product-detail-container a[href*="wishlist"]:not(.product-wishlist-button a),
        .product-detail-container a[href*="compare"],
        .product-detail-container .icon-heart,
        .product-detail-container .icon-repeat,
        .product-detail-container .btn-wishlist,
        .product-detail-container .btn-compare,
        .product-detail-container .compare-button,
        .product-detail-container .wishlist-button:not(.product-wishlist-button) {
            display: none !important;
        }
    </style>

    <style>
        :root {
            --primary-color: {{ theme_option('primary_color', '#fab528') }};
            --primary-color-rgb: {{ implode(', ', BaseHelper::hexToRgb(theme_option('primary_color', '#fab528'))) }};
            --heading-color: {{ theme_option('heading_color', '#000') }};
            --text-color: {{ theme_option('text_color', '#000') }};
            --primary-button-color: {{ theme_option('primary_button_color', '#000') }};
            --primary-button-background-color: {{ theme_option('primary_button_background_color') ?: theme_option('primary_color', '#fab528') }};
            --top-header-background-color: {{ theme_option('top_header_background_color', '#f7f7f7') }};
            --top-header-text-color: {{ theme_option('top_header_text_color') ?: theme_option('header_text_color', '#000') }};
            --middle-header-background-color: {{ theme_option('middle_header_background_color', '#fff') }};
            --middle-header-text-color: {{ theme_option('middle_header_text_color') ?: theme_option('header_text_color', '#000') }};
            --bottom-header-background-color: {{ theme_option('bottom_header_background_color', '#fff') }};
            --bottom-header-text-color: {{ theme_option('bottom_header_text_color') ?: theme_option('header_text_color', '#000') }};
            --header-text-color: {{ theme_option('header_text_color', '#000') }};
            --header-text-secondary-color: {{ BaseHelper::hexToRgba(theme_option('header_text_color', '#000'), 0.5) }};
            --header-deliver-color: {{ BaseHelper::hexToRgba(theme_option('header_deliver_color', '#000'), 0.15) }};
            --footer-text-color: {{ theme_option('footer_text_color', '#555') }};
            --footer-heading-color: {{ theme_option('footer_heading_color', '#555') }};
            --footer-hover-color: {{ theme_option('footer_hover_color', '#fab528') }};
            --footer-border-color: {{ theme_option('footer_border_color', '#dee2e6') }};
        }

        /* Checkout page fixes for all platforms */
        body.checkout-page {
            background-color: #f8f9fa !important;
        }

        .checkout-content-wrap {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* Product list styling */
        .checkout-page .cart-item {
            border-bottom: 1px solid #f1f1f1;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }

        .checkout-page .checkout-product-img-wrapper {
            width: 80px;
            height: 80px;
            overflow: hidden;
            border: 1px solid #f1f1f1;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .checkout-page .checkout-product-img-wrapper img {
            object-fit: cover;
            width: 100%;
            height: 100%;
            transition: transform 0.3s ease;
        }

        .checkout-page .checkout-product-img-wrapper:hover img {
            transform: scale(1.05);
        }

        .checkout-page .checkout-quantity {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #ff6633;
            color: white;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Quantity controls */
        .checkout-page .ec-checkout-quantity {
            border: 1px solid #e0e0e0;
            border-radius: 30px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100px;
            height: 36px;
            margin-top: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .checkout-page .ec-checkout-quantity:hover {
            border-color: #ff6633;
            box-shadow: 0 2px 8px rgba(255, 102, 51, 0.2);
        }

        .checkout-page .ec-checkout-quantity input {
            border: none;
            text-align: center;
            width: 40px;
            padding: 0;
            margin: 0;
            height: 100%;
            font-weight: bold;
            background-color: transparent;
        }

        .checkout-page .ec-checkout-quantity-control {
            cursor: pointer;
            width: 30px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            color: #ff6633;
            transition: all 0.2s ease;
        }

        .checkout-page .ec-checkout-quantity-control:hover {
            background-color: #fff0eb;
        }

        /* Hide the Price: section completely - using standard selectors */
        .checkout-page .small.d-flex.justify-content-between,
        .checkout-page .small + .small,
        .checkout-page small + small,
        .checkout-page div + .small,
        .checkout-page .ec-checkout-quantity + div,
        .checkout-page .ec-checkout-quantity + .small {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
        }

        /* Direct override for product options */
        .checkout-page .small.d-flex.justify-content-between,
        .checkout-page div.small[style*="display:block"],
        .checkout-page div.small[style*="display: block"],
        .checkout-page div.small:first-of-type,
        .checkout-page div.small:first-child {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
        }

        /* EXTREMELY AGGRESSIVE approach to hide the price section */
        /* Target any element that might contain the price */
        .checkout-page div,
        .checkout-page p,
        .checkout-page span,
        .checkout-page small,
        .checkout-page .small,
        .checkout-page [class*="small"],
        .checkout-page [class*="price"],
        .checkout-page [class*="Price"],
        .checkout-page [class*="option"],
        .checkout-page [class*="Option"] {
            /* Will be handled by JavaScript */
        }

        /* Direct targeting of the price section */
        .checkout-page .ec-checkout-quantity ~ div,
        .checkout-page .ec-checkout-quantity ~ p,
        .checkout-page .ec-checkout-quantity ~ span,
        .checkout-page .ec-checkout-quantity ~ small,
        .checkout-page .ec-checkout-quantity ~ .small,
        .checkout-page [data-bb-toggle="update-cart"] ~ div,
        .checkout-page [data-bb-toggle="update-cart"] ~ p,
        .checkout-page [data-bb-toggle="update-cart"] ~ span,
        .checkout-page [data-bb-toggle="update-cart"] ~ small,
        .checkout-page [data-bb-toggle="update-cart"] ~ .small {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            left: -9999px !important;
        }

        /* Override for the render-options-html template */
        .checkout-page [data-bb-toggle="update-cart"] ~ div,
        .checkout-page [data-bb-toggle="update-cart"] ~ div.small,
        .checkout-page [data-bb-toggle="update-cart"] ~ .small,
        .checkout-page .ec-checkout-quantity ~ div,
        .checkout-page .ec-checkout-quantity ~ div.small,
        .checkout-page .ec-checkout-quantity ~ .small {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
        }

        /* Price styling */
        .checkout-page .price-text {
            font-weight: bold;
            color: #ff6633;
        }

        .checkout-page .total-text {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff6633;
        }

        /* Improved price display in checkout */
        .checkout-page .checkout-price-display {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            margin-bottom: 5px;
        }

        .checkout-page .checkout-price-display .sale-price {
            color: #ff6633;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 2px;
        }

        .checkout-page .checkout-price-display .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 14px;
        }

        /* Hide Upper Subtotal Section */
        .checkout-page .upper-subtotal-section,
        .checkout-page .mt-2.p-2 > .row:not(.grand-total-section .row) {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            left: -9999px !important;
            opacity: 0 !important;
            pointer-events: none !important;
        }

        /* Grand Total Section Styling */
        .checkout-page .grand-total-section {
            border: 2px solid #003366 !important;
            border-radius: 8px !important;
            padding: 15px !important;
            background-color: #f8f9fa !important;
            margin-top: 20px !important;
            margin-bottom: 10px !important;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
        }

        .checkout-page .grand-total-title {
            color: #003366 !important;
            font-weight: bold !important;
            text-align: center !important;
            margin-top: 0 !important;
            margin-bottom: 15px !important;
            font-size: 18px !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
        }

        .checkout-page .grand-total-section .total-text {
            font-weight: bold !important;
            font-size: 18px !important;
            color: #003366 !important;
            margin-bottom: 0 !important;
        }

        /* Shipping method styling */
        .checkout-page .shipping-method-wrapper {
            border: 1px solid #f1f1f1;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .checkout-page .shipping-method-wrapper .radio-item {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            transition: all 0.2s;
        }

        .checkout-page .shipping-method-wrapper .radio-item:hover {
            background-color: #f8f9fa;
        }

        /* Checkout button */
        .checkout-page .payment-checkout-btn {
            background-color: #ff6633;
            border-color: #ff6633;
            padding: 10px 20px;
            font-weight: bold;
            border-radius: 5px;
        }

        .checkout-page .payment-checkout-btn:hover {
            background-color: #e55a2d;
            border-color: #e55a2d;
        }

        /* Custom search bar styling for desktop */
        @media (min-width: 992px) {
            .header-middle .header__left {
                width: 15%;
            }

            .header-middle .header__center {
                width: 70%;
            }

            .header-middle .header__right {
                width: 15%;
            }

            .header-middle .header__center .form--quick-search {
                border: 2px solid #ff6633;
                border-radius: 5px;
                width: 100%;
            }

            /* Fix for duplicate price in desktop view */
            .product-details .product__title + .bb-product-price,
            .product-details .star-rating + .bb-product-price,
            .product-details .d-none.d-md-block .product-price,
            .product-details .d-none.d-md-block .bb-product-price {
                display: none !important;
            }
        }

        /* Hide breadcrumbs on mobile and tablet */
        @media (max-width: 991px) {
            .page-breadcrumbs,
            nav[aria-label="breadcrumb"],
            .breadcrumb,
            .page-header .page-breadcrumbs,
            ol.breadcrumb,
            .breadcrumb-item {
                display: none !important;
                visibility: hidden !important;
                height: 0 !important;
                opacity: 0 !important;
                pointer-events: none !important;
                position: absolute !important;
                left: -9999px !important;
            }

            /* Mobile header styling */
            .header-mobile {
                background-color: #ff6633 !important;
                padding: 10px 15px !important;
                border-bottom: none !important;
            }

            /* Back button styling */
            .header-items-mobile--left .back-button a {
                color: white !important;
                font-size: 20px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .header-items-mobile--left .back-button .svg-icon svg {
                fill: white !important;
                width: 24px !important;
                height: 24px !important;
            }

            /* Search bar styling */
            .header-items-mobile--center .search-form--mobile {
                width: 100% !important;
            }

            .header-items-mobile--center .form--quick-search {
                width: 100% !important;
                border: none !important;
                border-radius: 5px !important;
                overflow: hidden !important;
            }

            .header-items-mobile--center .form--quick-search input {
                height: 40px !important;
                border-radius: 5px 0 0 5px !important;
                padding: 5px 15px !important;
                background-color: white !important;
                border: none !important;
                width: calc(100% - 40px) !important;
                float: left !important;
            }

            .header-items-mobile--center .form--quick-search .search-wrapper {
                display: flex !important;
                position: relative !important;
                width: 100% !important;
            }

            .header-items-mobile--center .form--quick-search .btn-search {
                width: 40px !important;
                height: 40px !important;
                background-color: white !important;
                border: none !important;
                border-radius: 0 5px 5px 0 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                padding: 0 !important;
            }

            .header-items-mobile--center .form--quick-search .btn-search .svg-icon {
                width: 20px !important;
                height: 20px !important;
            }

            .header-items-mobile--center .form--quick-search .btn-search .svg-icon svg {
                fill: #ff6633 !important;
            }

            /* Menu icon styling */
            .header-items-mobile--right .menu-mobile .menu-icon {
                color: white !important;
            }

            .header-items-mobile--right .menu-mobile .svg-icon svg {
                fill: white !important;
                width: 24px !important;
                height: 24px !important;
            }

            /* Adjust widths for the three sections */
            .header-items-mobile--left {
                width: 15% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: flex-start !important;
            }

            .header-items-mobile--center {
                width: 70% !important;
            }

            .header-items-mobile--right {
                width: 15% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: flex-end !important;
            }

            /* Homepage specific styling */
            body.home .header-items-mobile--left .menu-mobile .menu-icon {
                color: white !important;
            }

            body.home .header-items-mobile--left .menu-mobile .svg-icon svg {
                fill: white !important;
                width: 24px !important;
                height: 24px !important;
            }

            /* Hide grid/list view selection in mobile and tablet */
            .catalog-toolbar__view .toolbar-view__icon,
            .store-toolbar__view .toolbar-view__icon,
            .catalog-toolbar__view .d-flex,
            .store-toolbar__view .d-flex {
                display: none !important;
            }

            /* Mobile-specific checkout fixes */

            /* Responsive checkout fixes for mobile */
            .checkout-page .order-1 {
                order: 1 !important;
            }

            .checkout-page .order-md-2 {
                order: 2 !important;
            }

            .checkout-page .payment-checkout-btn {
                width: 100%;
                margin-top: 15px;
            }

            .checkout-page .ec-checkout-quantity {
                width: 90px;
            }
        }
    </style>

    @php
        Theme::asset()->remove('language-css');
        Theme::asset()
            ->container('footer')
            ->remove('language-public-js');
        Theme::asset()
            ->container('footer')
            ->remove('simple-slider-owl-carousel-css');
        Theme::asset()
            ->container('footer')
            ->remove('simple-slider-owl-carousel-js');
        Theme::asset()
            ->container('footer')
            ->remove('simple-slider-css');
        Theme::asset()
            ->container('footer')
            ->remove('simple-slider-js');
    @endphp

    {!! Theme::header() !!}
    <link rel="stylesheet" href="{{ asset('themes/farmart/css/product-text-colors.css') }}">
</head>

<body {!! Theme::bodyAttributes() !!}>
    @if (theme_option('preloader_enabled', 'yes') == 'yes')
        {!! Theme::partial('preloader') !!}
    @endif

    {!! Theme::partial('svg-icons') !!}
    {!! apply_filters(THEME_FRONT_BODY, null) !!}

    <header
        class="header header-js-handler"
        data-sticky="{{ theme_option('sticky_header_enabled', 'yes') == 'yes' ? 'true' : 'false' }}"
    >
        <div @class([
            'header-top d-none d-lg-block',
            'header-content-sticky' =>
                theme_option('sticky_header_content_position', 'middle') == 'top',
        ])>
            <div class="container-xxxl">
                <div class="header-wrapper">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <div class="header-info">
                                {!! Menu::renderMenuLocation('header-navigation', ['view' => 'menu-default']) !!}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="header-info header-info-right">
                                <ul>
                                    @if (is_plugin_active('language'))
                                        {!! Theme::partial('language-switcher') !!}
                                    @endif
                                    @if (is_plugin_active('ecommerce'))
                                        @if (count($currencies) > 1)
                                            <li>
                                                <a
                                                    class="language-dropdown-active"
                                                    href="#"
                                                >
                                                    <span>{{ get_application_currency()->title }}</span>
                                                    <span class="svg-icon">
                                                        <svg>
                                                            <use
                                                                href="#svg-icon-chevron-down"
                                                                xlink:href="#svg-icon-chevron-down"
                                                            ></use>
                                                        </svg>
                                                    </span>
                                                </a>
                                                <ul class="language-dropdown">
                                                    @foreach ($currencies as $currency)
                                                        @if ($currency->id !== get_application_currency_id())
                                                            <li>
                                                                <a
                                                                    href="{{ route('public.change-currency', $currency->title) }}">
                                                                    <span>{{ $currency->title }}</span>
                                                                </a>
                                                            </li>
                                                        @endif
                                                    @endforeach
                                                </ul>
                                            </li>
                                        @endif
                                        @if (auth('customer')->check())
                                            <li>
                                                <a
                                                    href="{{ route('customer.overview') }}">{{ auth('customer')->user()->name }}</a>
                                                <span class="d-inline-block ms-1">(<a
                                                        class="color-primary"
                                                        href="{{ route('customer.logout') }}"
                                                    >{{ __('Logout') }}</a>)</span>
                                            </li>
                                        @else
                                            <li><a href="{{ route('customer.login') }}">{{ __('Login') }}</a></li>
                                            <li><a href="{{ route('customer.register') }}">{{ __('Register') }}</a>
                                            </li>
                                        @endif
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div @class([
            'header-middle',
            'header-content-sticky' =>
                theme_option('sticky_header_content_position', 'middle') == 'middle',
        ])>
            <div class="container-xxxl">
                <div class="header-wrapper d-flex align-items-center" style="gap: 24px;">
                    <div class="header-items header__left d-flex align-items-center etsy-header-left" style="gap: 16px;">
                        <div class="logo etsy-logo">
                            <a href="{{ BaseHelper::getHomepageUrl() }}">
                                {!! Theme::getLogoImage(['style' => 'max-height: 45px']) !!}
                            </a>
                        </div>
                        @if (is_plugin_active('ecommerce') && theme_option('enabled_product_categories_on_header', 'yes') == 'yes')
                            <div class="menu--product-categories etsy-categories-btn">
                                <div class="menu__toggle etsy-categories-toggle">
                                    <span class="svg-icon etsy-categories-icon">
                                        <svg>
                                            <use href="#svg-icon-list" xlink:href="#svg-icon-list"></use>
                                        </svg>
                                    </span>
                                    <span class="menu__toggle-title etsy-categories-title">{{ __('CATEGORIES') }}</span>
                                </div>
                                <div
                                    class="menu__content"
                                    data-bb-toggle="init-categories-dropdown"
                                    data-bb-target=".menu--dropdown"
                                    data-url="{{ route('public.ajax.categories-dropdown') }}"
                                >
                                    <ul class="menu--dropdown"></ul>
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="header-items header__center flex-grow-1 etsy-header-center">
                        @if (is_plugin_active('ecommerce'))
                            <x-plugins-ecommerce::fronts.ajax-search class="form--quick-search etsy-search-bar">
                                <x-plugins-ecommerce::fronts.ajax-search.input type="text" class="form-control input-search-product etsy-search-input" placeholder="{{ __('Search for anything') }}" />
                                <button class="btn etsy-search-btn" type="submit" aria-label="Submit">
                                    <span class="svg-icon etsy-search-icon">
                                        <svg>
                                            <use href="#svg-icon-search" xlink:href="#svg-icon-search"></use>
                                        </svg>
                                    </span>
                                </button>
                            </x-plugins-ecommerce::fronts.ajax-search>
                        @endif
                    </div>
                    <div class="header-items header__right d-flex align-items-center etsy-header-right" style="gap: 16px;">
                        @if (is_plugin_active('ecommerce'))
                            @if (EcommerceHelper::isWishlistEnabled())
                                <div class="header__extra header-wishlist etsy-icon-btn">
                                    <a class="btn-wishlist" href="{{ route('public.wishlist') }}">
                                        <span class="svg-icon">
                                            <svg>
                                                <use href="#svg-icon-wishlist" xlink:href="#svg-icon-wishlist"></use>
                                            </svg>
                                        </span>
                                        <span class="header-item-counter etsy-badge">
                                            {{ auth('customer')->check()? auth('customer')->user()->wishlist()->count(): Cart::instance('wishlist')->count() }}
                                        </span>
                                    </a>
                                </div>
                            @endif
                            @if (EcommerceHelper::isCartEnabled())
                                <div class="header__extra cart--mini etsy-icon-btn" role="button" tabindex="0">
                                    <div class="header__extra">
                                        <a class="btn-shopping-cart" href="{{ route('public.cart') }}">
                                            <span class="svg-icon">
                                                <svg>
                                                    <use href="#svg-icon-cart" xlink:href="#svg-icon-cart"></use>
                                                </svg>
                                            </span>
                                            <span class="header-item-counter etsy-badge">{{ Cart::instance('cart')->count() }}</span>
                                        </a>
                                    </div>
                                </div>
                            @endif
                        @endif
                        @if (auth('customer')->check())
                            <a class="etsy-signin" href="{{ route('customer.overview') }}">{{ __('Sign in') }}</a>
                        @else
                            <a class="etsy-signin" href="{{ route('customer.login') }}">{{ __('Sign in') }}</a>
                        @endif
                    </div>
                </div>
                <div class="etsy-nav-row mt-2 d-flex justify-content-center align-items-center" style="gap: 32px;">
                    {!! Menu::renderMenuLocation('main-menu', [
                        'view' => 'menu',
                        'options' => ['class' => 'menu d-flex flex-row align-items-center etsy-nav-menu', 'style' => 'gap: 32px; margin-bottom: 0;'],
                    ]) !!}
                </div>
            </div>
        </div>
        <div
            class="header-mobile header-js-handler"
            data-sticky="{{ theme_option('sticky_header_mobile_enabled', 'yes') == 'yes' ? 'true' : 'false' }}"
        >
            <div class="header-items-mobile header-items-mobile--left">
                @if (!request()->is('/'))
                <div class="back-button">
                    <a href="javascript:history.back()">
                        <span class="svg-icon">
                            <svg>
                                <use
                                    href="#svg-icon-arrow-left"
                                    xlink:href="#svg-icon-arrow-left"
                                ></use>
                            </svg>
                        </span>
                    </a>
                </div>
                @else
                <div class="menu-mobile">
                    <div class="menu-box-title">
                        <div
                            class="icon menu-icon toggle--sidebar"
                            href="#menu-mobile"
                        >
                            <span class="svg-icon" style="color: #fff;">
                                <svg>
                                    <use
                                        href="#svg-icon-list"
                                        xlink:href="#svg-icon-list"
                                    ></use>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                @endif
            </div>
            <div class="header-items-mobile header-items-mobile--center">
                <div class="search-form--mobile">
                    <form class="form--quick-search" action="{{ route('public.products') }}" method="GET">
                        <div class="search-wrapper">
                            <input class="form-control input-search-product" name="q" type="text" placeholder="{{ __('Search products...') }}" autocomplete="off">
                            <button class="btn-search" type="submit">
                                <span class="svg-icon">
                                    <svg>
                                        <use href="#svg-icon-search" xlink:href="#svg-icon-search"></use>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="header-items-mobile header-items-mobile--right">
                @if (!request()->is('/'))
                <div class="menu-mobile">
                    <div class="menu-box-title">
                        <div
                            class="icon menu-icon toggle--sidebar"
                            href="#menu-mobile"
                        >
                            <span class="svg-icon">
                                <svg>
                                    <use
                                        href="#svg-icon-list"
                                        xlink:href="#svg-icon-list"
                                    ></use>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </header>
