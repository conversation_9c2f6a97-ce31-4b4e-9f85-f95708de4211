// Fix for quantity input functionality
document.addEventListener('DOMContentLoaded', function() {
    // Function to fix quantity input
    function fixQuantityInput() {
        // Get all quantity inputs
        const quantityInputs = document.querySelectorAll('.qty');
        
        // Set initial value to 1
        quantityInputs.forEach(function(input) {
            // Force the value to be 1
            input.value = 1;
            
            // Add event listener to ensure value is always at least 1
            input.addEventListener('change', function() {
                const value = parseInt(this.value);
                if (isNaN(value) || value < 1) {
                    this.value = 1;
                }
            });
        });
        
        // Add event listeners to increase/decrease buttons
        const decreaseButtons = document.querySelectorAll('.decrease');
        const increaseButtons = document.querySelectorAll('.increase');
        
        decreaseButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const input = this.closest('.qty-box').querySelector('.qty');
                let value = parseInt(input.value);
                if (!isNaN(value) && value > 1) {
                    input.value = value - 1;
                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                }
            });
        });
        
        increaseButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const input = this.closest('.qty-box').querySelector('.qty');
                let value = parseInt(input.value);
                if (!isNaN(value)) {
                    input.value = value + 1;
                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                }
            });
        });
    }
    
    // Run on page load
    fixQuantityInput();
    
    // Run after a short delay to ensure all elements are loaded
    setTimeout(fixQuantityInput, 500);
});
