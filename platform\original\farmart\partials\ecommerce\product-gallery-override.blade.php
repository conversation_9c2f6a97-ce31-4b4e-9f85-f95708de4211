@php
    EcommerceHelper::registerThemeAssets();
    $version = get_cms_version();
    Theme::asset()->add('lightgallery-css', 'vendor/core/plugins/ecommerce/libraries/lightgallery/css/lightgallery.min.css', version: $version);
    Theme::asset()->add('slick-css', 'vendor/core/plugins/ecommerce/libraries/slick/slick.css', version: $version);
    Theme::asset()->container('footer')->add('lightgallery-js', 'vendor/core/plugins/ecommerce/libraries/lightgallery/js/lightgallery.min.js', ['jquery'], version: $version);
    Theme::asset()->container('footer')->add('slick-js', 'vendor/core/plugins/ecommerce/libraries/slick/slick.min.js', ['jquery'], version: $version);

    $galleryStyle = theme_option('ecommerce_product_gallery_image_style', 'vertical');

    // Count total images and videos
    $totalImages = count($productImages);
    $totalVideos = 0;
    foreach($product->video as $video) {
        if ($video['url']) {
            $totalVideos++;
        }
    }
    $totalMedia = $totalImages + $totalVideos;
@endphp

<div class="bb-product-gallery-wrapper">
    <div @class(['bb-product-gallery', 'bb-product-gallery-' . $galleryStyle])>
        <div class="bb-product-gallery-images position-relative">
            @if (! empty($product->video))
                @foreach($product->video as $video)
                    @continue(! $video['url'])

                    <div class="bb-product-video">
                        @if ($video['provider'] === 'video')
                            <video
                                id="{{ md5($video['url']) }}"
                                playsinline="playsinline"
                                mute="true"
                                preload="auto"
                                class="media-video"
                                aria-label="{{ $product->name }}"
                                poster="{{ $video['thumbnail'] }}"
                            >
                                <source src="{{ $video['url'] }}" type="video/{{ File::extension($video['url']) ?: 'mp4' }}">
                                <img src="{{ $video['thumbnail'] }}" alt="{{ $video['url'] }}">
                            </video>
                            <button class="bb-button-trigger-play-video" data-target="{{ md5($video['url']) }}">
                                <x-core::icon name="ti ti-player-play-filled" />
                            </button>
                        @else
                            <iframe
                                data-provider="{{ $video['provider'] }}"
                                src="{{ $video['url'] }}"
                                frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen>
                            </iframe>
                        @endif
                    </div>
                @endforeach
            @endif
            @foreach ($productImages as $image)
                <a href="{{ RvMedia::getImageUrl($image) }}">
                    {{ RvMedia::image($image, $product->name) }}
                </a>
            @endforeach

            <!-- Image Counter -->
            <div class="image-counter">
                <span class="current-image">1</span>/<span class="total-images">{{ $totalMedia }}</span>
            </div>

            <!-- Navigation Arrows -->
            <div class="gallery-nav gallery-nav-prev">
                <button class="btn-gallery-nav btn-prev">
                    <span class="svg-icon">
                        <svg>
                            <use href="#svg-icon-chevron-left" xlink:href="#svg-icon-chevron-left"></use>
                        </svg>
                    </span>
                </button>
            </div>
            <div class="gallery-nav gallery-nav-next">
                <button class="btn-gallery-nav btn-next">
                    <span class="svg-icon">
                        <svg>
                            <use href="#svg-icon-chevron-right" xlink:href="#svg-icon-chevron-right"></use>
                        </svg>
                    </span>
                </button>
            </div>
        </div>
        <div class="bb-product-gallery-thumbnails" data-vertical="{{ $galleryStyle === 'vertical' ? 1 : 0 }}">
            @foreach($product->video as $video)
                @continue(! $video['url'])

                <div class="video-thumbnail">
                    <img src="{{ $video['thumbnail'] }}" alt="{{ $product->name }}">
                    <x-core::icon name="ti ti-player-play-filled" />
                </div>
            @endforeach
            @foreach ($productImages as $image)
                <div>
                    {{ RvMedia::image($image, $product->name, 'thumb') }}
                </div>
            @endforeach
        </div>
    </div>
</div>

<style>
    /* Image Counter Styles */
    .image-counter {
        position: absolute !important;
        bottom: 15px !important;
        right: 15px !important;
        background-color: rgba(0, 0, 0, 0.6) !important;
        color: #fff !important;
        padding: 5px 10px !important;
        border-radius: 15px !important;
        font-size: 12px !important;
        z-index: 999 !important;
        display: block !important;
    }

    /* Navigation Arrows Styles */
    .gallery-nav {
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 999 !important;
        display: block !important;
    }

    .gallery-nav-prev {
        left: 10px !important;
    }

    .gallery-nav-next {
        right: 10px !important;
    }

    .btn-gallery-nav {
        background-color: rgba(255, 255, 255, 0.7) !important;
        border: none !important;
        border-radius: 50% !important;
        width: 40px !important;
        height: 40px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .btn-gallery-nav:hover {
        background-color: rgba(255, 255, 255, 0.9) !important;
    }

    .btn-gallery-nav .svg-icon {
        width: 20px !important;
        height: 20px !important;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Make sure the elements are visible
        $('.image-counter, .gallery-nav').css('display', 'block');

        // Wait for slick to be initialized
        setTimeout(function() {
            // Update the current image number when the slide changes
            $('.bb-product-gallery-images').on('afterChange', function(event, slick, currentSlide) {
                $('.current-image').text(currentSlide + 1);
            });

            // Add click handlers for the navigation buttons
            $('.btn-prev').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-product-gallery-images').slick('slickPrev');
                return false;
            });

            $('.btn-next').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-product-gallery-images').slick('slickNext');
                return false;
            });
        }, 1000); // Wait 1 second for slick to initialize
    });
</script>
