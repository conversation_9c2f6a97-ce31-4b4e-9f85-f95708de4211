// The following features have been deprecated and will be removed in the next MAJOR version release

@use "sass:math";

@mixin inline-block {
    display: inline-block;

    @warn "The inline-block mixin is deprecated and will be removed in the next major version release";
}

@mixin button($style: simple, $base-color: #4294f0, $text-size: inherit, $padding: 7px 18px) {

    @if type-of($style) == string and type-of($base-color) == color {
        @include buttonstyle($style, $base-color, $text-size, $padding);
    }

    @if type-of($style) == string and type-of($base-color) == number {
        $padding: $text-size;
        $text-size: $base-color;
        $base-color: #4294f0;

        @if $padding == inherit {
            $padding: 7px 18px;
        }

        @include buttonstyle($style, $base-color, $text-size, $padding);
    }

    @if type-of($style) == color and type-of($base-color) == color {
        $base-color: $style;
        $style: simple;
        @include buttonstyle($style, $base-color, $text-size, $padding);
    }

    @if type-of($style) == color and type-of($base-color) == number {
        $padding: $text-size;
        $text-size: $base-color;
        $base-color: $style;
        $style: simple;

        @if $padding == inherit {
            $padding: 7px 18px;
        }

        @include buttonstyle($style, $base-color, $text-size, $padding);
    }

    @if type-of($style) == number {
        $padding: $base-color;
        $text-size: $style;
        $base-color: #4294f0;
        $style: simple;

        @if $padding == #4294f0 {
            $padding: 7px 18px;
        }

        @include buttonstyle($style, $base-color, $text-size, $padding);
    }

    &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }

    @warn "The button mixin is deprecated and will be removed in the next major version release";
}

// Selector Style Button
@mixin buttonstyle($type, $b-color, $t-size, $pad) {
    // Grayscale button
    @if $type == simple and $b-color == grayscale($b-color) {
        @include simple($b-color, true, $t-size, $pad);
    }

    @if $type == shiny and $b-color == grayscale($b-color) {
        @include shiny($b-color, true, $t-size, $pad);
    }

    @if $type == pill and $b-color == grayscale($b-color) {
        @include pill($b-color, true, $t-size, $pad);
    }

    @if $type == flat and $b-color == grayscale($b-color) {
        @include flat($b-color, true, $t-size, $pad);
    }

    // Colored button
    @if $type == simple {
        @include simple($b-color, false, $t-size, $pad);
    } @else if $type == shiny {
        @include shiny($b-color, false, $t-size, $pad);
    } @else if $type == pill {
        @include pill($b-color, false, $t-size, $pad);
    } @else if $type == flat {
        @include flat($b-color, false, $t-size, $pad);
    }
}

// Simple Button
@mixin simple($base-color, $grayscale: false, $textsize: inherit, $padding: 7px 18px) {
    $color: hsl(0, 0, 100%);
    $border: adjust-color($base-color, $saturation: 9%, $lightness: -14%);
    $inset-shadow: adjust-color($base-color, $saturation: -8%, $lightness: 15%);
    $stop-gradient: adjust-color($base-color, $saturation: 9%, $lightness: -11%);
    $text-shadow: adjust-color($base-color, $saturation: 15%, $lightness: -18%);

    @if is-light($base-color) {
        $color: hsl(0, 0, 20%);
        $text-shadow: adjust-color($base-color, $saturation: 10%, $lightness: 4%);
    }

    @if $grayscale == true {
        $border: grayscale($border);
        $inset-shadow: grayscale($inset-shadow);
        $stop-gradient: grayscale($stop-gradient);
        $text-shadow: grayscale($text-shadow);
    }

    border: 1px solid $border;
    border-radius: 3px;
    box-shadow: inset 0 1px 0 0 $inset-shadow;
    color: $color;
    display: inline-block;
    font-size: $textsize;
    font-weight: bold;
    @include linear-gradient($base-color, $stop-gradient);
    padding: $padding;
    text-decoration: none;
    text-shadow: 0 1px 0 $text-shadow;
    background-clip: padding-box;

    &:hover:not(:disabled) {
        $base-color-hover: adjust-color($base-color, $saturation: -4%, $lightness: -5%);
        $inset-shadow-hover: adjust-color($base-color, $saturation: -7%, $lightness: 5%);
        $stop-gradient-hover: adjust-color($base-color, $saturation: 8%, $lightness: -14%);

        @if $grayscale == true {
            $base-color-hover: grayscale($base-color-hover);
            $inset-shadow-hover: grayscale($inset-shadow-hover);
            $stop-gradient-hover: grayscale($stop-gradient-hover);
        }

        @include linear-gradient($base-color-hover, $stop-gradient-hover);

        box-shadow: inset 0 1px 0 0 $inset-shadow-hover;
        cursor: pointer;
    }

    &:active:not(:disabled),
    &:focus:not(:disabled) {
        $border-active: adjust-color($base-color, $saturation: 9%, $lightness: -14%);
        $inset-shadow-active: adjust-color($base-color, $saturation: 7%, $lightness: -17%);

        @if $grayscale == true {
            $border-active: grayscale($border-active);
            $inset-shadow-active: grayscale($inset-shadow-active);
        }

        border: 1px solid $border-active;
        box-shadow: inset 0 0 8px 4px $inset-shadow-active, inset 0 0 8px 4px $inset-shadow-active;
    }
}

// Shiny Button
@mixin shiny($base-color, $grayscale: false, $textsize: inherit, $padding: 7px 18px) {
    $color: hsl(0, 0, 100%);
    $border: adjust-color($base-color, $red: -117, $green: -111, $blue: -81);
    $border-bottom: adjust-color($base-color, $red: -126, $green: -127, $blue: -122);
    $fourth-stop: adjust-color($base-color, $red: -79, $green: -70, $blue: -46);
    $inset-shadow: adjust-color($base-color, $red: 37, $green: 29, $blue: 12);
    $second-stop: adjust-color($base-color, $red: -56, $green: -50, $blue: -33);
    $text-shadow: adjust-color($base-color, $red: -140, $green: -141, $blue: -114);
    $third-stop: adjust-color($base-color, $red: -86, $green: -75, $blue: -48);

    @if is-light($base-color) {
        $color: hsl(0, 0, 20%);
        $text-shadow: adjust-color($base-color, $saturation: 10%, $lightness: 4%);
    }

    @if $grayscale == true {
        $border: grayscale($border);
        $border-bottom: grayscale($border-bottom);
        $fourth-stop: grayscale($fourth-stop);
        $inset-shadow: grayscale($inset-shadow);
        $second-stop: grayscale($second-stop);
        $text-shadow: grayscale($text-shadow);
        $third-stop: grayscale($third-stop);
    }

    @include linear-gradient(top, $base-color 0%, $second-stop 50%, $third-stop 50%, $fourth-stop 100%);

    border: 1px solid $border;
    border-bottom: 1px solid $border-bottom;
    border-radius: 5px;
    box-shadow: inset 0 1px 0 0 $inset-shadow;
    color: $color;
    display: inline-block;
    font-size: $textsize;
    font-weight: bold;
    padding: $padding;
    text-align: center;
    text-decoration: none;
    text-shadow: 0 -1px 1px $text-shadow;

    &:hover:not(:disabled) {
        $first-stop-hover: adjust-color($base-color, $red: -13, $green: -15, $blue: -18);
        $second-stop-hover: adjust-color($base-color, $red: -66, $green: -62, $blue: -51);
        $third-stop-hover: adjust-color($base-color, $red: -93, $green: -85, $blue: -66);
        $fourth-stop-hover: adjust-color($base-color, $red: -86, $green: -80, $blue: -63);

        @if $grayscale == true {
            $first-stop-hover: grayscale($first-stop-hover);
            $second-stop-hover: grayscale($second-stop-hover);
            $third-stop-hover: grayscale($third-stop-hover);
            $fourth-stop-hover: grayscale($fourth-stop-hover);
        }

        @include linear-gradient(top, $first-stop-hover 0%,
            $second-stop-hover 50%,
            $third-stop-hover 50%,
            $fourth-stop-hover 100%);
        cursor: pointer;
    }

    &:active:not(:disabled),
    &:focus:not(:disabled) {
        $inset-shadow-active: adjust-color($base-color, $red: -111, $green: -116, $blue: -122);

        @if $grayscale == true {
            $inset-shadow-active: grayscale($inset-shadow-active);
        }

        box-shadow: inset 0 0 20px 0 $inset-shadow-active;
    }
}

// Pill Button
@mixin pill($base-color, $grayscale: false, $textsize: inherit, $padding: 7px 18px) {
    $color: hsl(0, 0, 100%);
    $border-bottom: adjust-color($base-color, $hue: 8, $saturation: -11%, $lightness: -26%);
    $border-sides: adjust-color($base-color, $hue: 4, $saturation: -21%, $lightness: -21%);
    $border-top: adjust-color($base-color, $hue: -1, $saturation: -30%, $lightness: -15%);
    $inset-shadow: adjust-color($base-color, $hue: -1, $saturation: -1%, $lightness: 7%);
    $stop-gradient: adjust-color($base-color, $hue: 8, $saturation: 14%, $lightness: -10%);
    $text-shadow: adjust-color($base-color, $hue: 5, $saturation: -19%, $lightness: -15%);

    @if is-light($base-color) {
        $color: hsl(0, 0, 20%);
        $text-shadow: adjust-color($base-color, $saturation: 10%, $lightness: 4%);
    }

    @if $grayscale == true {
        $border-bottom: grayscale($border-bottom);
        $border-sides: grayscale($border-sides);
        $border-top: grayscale($border-top);
        $inset-shadow: grayscale($inset-shadow);
        $stop-gradient: grayscale($stop-gradient);
        $text-shadow: grayscale($text-shadow);
    }

    border: 1px solid $border-top;
    border-color: $border-top $border-sides $border-bottom;
    border-radius: 16px;
    box-shadow: inset 0 1px 0 0 $inset-shadow;
    color: $color;
    display: inline-block;
    font-size: $textsize;
    font-weight: normal;
    line-height: 1;
    @include linear-gradient($base-color, $stop-gradient);
    padding: $padding;
    text-align: center;
    text-decoration: none;
    text-shadow: 0 -1px 1px $text-shadow;
    background-clip: padding-box;

    &:hover:not(:disabled) {
        $base-color-hover: adjust-color($base-color, $lightness: -4.5%);
        $border-bottom: adjust-color($base-color, $hue: 8, $saturation: 13.5%, $lightness: -32%);
        $border-sides: adjust-color($base-color, $hue: 4, $saturation: -2%, $lightness: -27%);
        $border-top: adjust-color($base-color, $hue: -1, $saturation: -17%, $lightness: -21%);
        $inset-shadow-hover: adjust-color($base-color, $saturation: -1%, $lightness: 3%);
        $stop-gradient-hover: adjust-color($base-color, $hue: 8, $saturation: -4%, $lightness: -15.5%);
        $text-shadow-hover: adjust-color($base-color, $hue: 5, $saturation: -5%, $lightness: -22%);

        @if $grayscale == true {
            $base-color-hover: grayscale($base-color-hover);
            $border-bottom: grayscale($border-bottom);
            $border-sides: grayscale($border-sides);
            $border-top: grayscale($border-top);
            $inset-shadow-hover: grayscale($inset-shadow-hover);
            $stop-gradient-hover: grayscale($stop-gradient-hover);
            $text-shadow-hover: grayscale($text-shadow-hover);
        }

        @include linear-gradient($base-color-hover, $stop-gradient-hover);

        background-clip: padding-box;
        border: 1px solid $border-top;
        border-color: $border-top $border-sides $border-bottom;
        box-shadow: inset 0 1px 0 0 $inset-shadow-hover;
        cursor: pointer;
        text-shadow: 0 -1px 1px $text-shadow-hover;
    }

    &:active:not(:disabled),
    &:focus:not(:disabled) {
        $active-color: adjust-color($base-color, $hue: 4, $saturation: -12%, $lightness: -10%);
        $border-active: adjust-color($base-color, $hue: 6, $saturation: -2.5%, $lightness: -30%);
        $border-bottom-active: adjust-color($base-color, $hue: 11, $saturation: 6%, $lightness: -31%);
        $inset-shadow-active: adjust-color($base-color, $hue: 9, $saturation: 2%, $lightness: -21.5%);
        $text-shadow-active: adjust-color($base-color, $hue: 5, $saturation: -12%, $lightness: -21.5%);

        @if $grayscale == true {
            $active-color: grayscale($active-color);
            $border-active: grayscale($border-active);
            $border-bottom-active: grayscale($border-bottom-active);
            $inset-shadow-active: grayscale($inset-shadow-active);
            $text-shadow-active: grayscale($text-shadow-active);
        }

        background: $active-color;
        border: 1px solid $border-active;
        border-bottom: 1px solid $border-bottom-active;
        box-shadow: inset 0 0 6px 3px $inset-shadow-active;
        text-shadow: 0 -1px 1px $text-shadow-active;
    }
}

// Flat Button
@mixin flat($base-color, $grayscale: false, $textsize: inherit, $padding: 7px 18px) {
    $color: hsl(0, 0, 100%);

    @if is-light($base-color) {
        $color: hsl(0, 0, 20%);
    }

    background-color: $base-color;
    border-radius: 3px;
    border: 0;
    color: $color;
    display: inline-block;
    font-size: $textsize;
    font-weight: bold;
    padding: $padding;
    text-decoration: none;
    background-clip: padding-box;

    &:hover:not(:disabled) {
        $base-color-hover: adjust-color($base-color, $saturation: 4%, $lightness: 5%);

        @if $grayscale == true {
            $base-color-hover: grayscale($base-color-hover);
        }

        background-color: $base-color-hover;
        cursor: pointer;
    }

    &:active:not(:disabled),
    &:focus:not(:disabled) {
        $base-color-active: adjust-color($base-color, $saturation: -4%, $lightness: -5%);

        @if $grayscale == true {
            $base-color-active: grayscale($base-color-active);
        }

        background-color: $base-color-active;
        cursor: pointer;
    }
}

// Flexible grid
@function flex-grid($columns, $container-columns: $fg-max-columns) {
    $width: $columns * $fg-column + ($columns - 1) * $fg-gutter;
    $container-width: $container-columns * $fg-column + ($container-columns - 1) * $fg-gutter;
    @return percentage(math.div($width, $container-width));

    @warn "The flex-grid function is deprecated and will be removed in the next major version release";
}

// Flexible gutter
@function flex-gutter($container-columns: $fg-max-columns, $gutter: $fg-gutter) {
    $container-width: $container-columns * $fg-column + ($container-columns - 1) * $fg-gutter;
    @return percentage(math.div($gutter, $container-width));

    @warn "The flex-gutter function is deprecated and will be removed in the next major version release";
}

@function grid-width($n) {
    @return $n * $gw-column + ($n - 1) * $gw-gutter;

    @warn "The grid-width function is deprecated and will be removed in the next major version release";
}

@function golden-ratio($value, $increment) {
    @return modular-scale($increment, $value, $ratio: $golden);

    @warn "The golden-ratio function is deprecated and will be removed in the next major version release. Please use the modular-scale function, instead.";
}

@mixin box-sizing($box) {
    @include prefixer(box-sizing, $box, webkit moz spec);

    @warn "The box-sizing mixin is deprecated and will be removed in the next major version release. This property can now be used un-prefixed.";
}
