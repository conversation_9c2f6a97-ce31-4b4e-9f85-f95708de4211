/**
 * Global CSS to completely disable add to cart hover functionality
 * This file uses extremely aggressive selectors and !important flags
 */

/* HIDE ALL PRODUCT BOTTOM BOXES - CONTAINS ADD TO CART */
.product-inner .product-bottom-box,
.products .product-inner .product-bottom-box,
.product-inner:hover .product-bottom-box,
.products .product-inner:hover .product-bottom-box,
.product-bottom-box,
*[class*="product"] .product-bottom-box,
*[class*="product"]:hover .product-bottom-box {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}

/* HIDE ALL PRODUCT LOOP BUTTONS */
.product-inner .product-loop__buttons,
.products .product-inner .product-loop__buttons,
.product-inner:hover .product-loop__buttons,
.products .product-inner:hover .product-loop__buttons,
.product-loop__buttons,
*[class*="product"] .product-loop__buttons,
*[class*="product"]:hover .product-loop__buttons {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}

/* PREVENT HOVER EFFECTS ON PRODUCT CARDS */
.product-inner,
.products .product-inner,
*[class*="product"] {
    overflow: hidden !important;
    position: relative !important;
}

.product-inner:hover,
.products .product-inner:hover,
*[class*="product"]:hover {
    border-color: transparent !important;
    position: static !important;
    z-index: 1 !important;
}

/* HIDE QUANTITY INPUTS AND ADD TO CART BUTTONS */
.product-inner input[type="number"],
.product-inner .quantity-input,
.product-inner .add-to-cart,
.product-inner .add_to_cart_button,
.product-inner .add-to-cart-button,
.product-inner button[type="submit"],
.product-inner .cart-form,
.product-inner .add_to_cart,
.product-inner .btn-add-to-cart,
.product-inner .btn-add-cart,
.product-inner .btn-cart,
.product-inner .btn-add-to-cart-wrapper,
.product-inner .btn-add-cart-wrapper,
.product-inner .btn-cart-wrapper,
.products .product-inner input[type="number"],
.products .product-inner .quantity-input,
.products .product-inner .add-to-cart,
.products .product-inner .add_to_cart_button,
.products .product-inner .add-to-cart-button,
.products .product-inner button[type="submit"],
.products .product-inner .cart-form,
.products .product-inner .add_to_cart,
.products .product-inner .btn-add-to-cart,
.products .product-inner .btn-add-cart,
.products .product-inner .btn-cart,
.products .product-inner .btn-add-to-cart-wrapper,
.products .product-inner .btn-add-cart-wrapper,
.products .product-inner .btn-cart-wrapper,
*[class*="product"] input[type="number"],
*[class*="product"] .quantity-input,
*[class*="product"] .add-to-cart,
*[class*="product"] .add_to_cart_button,
*[class*="product"] .add-to-cart-button,
*[class*="product"] button[type="submit"],
*[class*="product"] .cart-form,
*[class*="product"] .add_to_cart,
*[class*="product"] .btn-add-to-cart,
*[class*="product"] .btn-add-cart,
*[class*="product"] .btn-cart,
*[class*="product"] .btn-add-to-cart-wrapper,
*[class*="product"] .btn-add-cart-wrapper,
*[class*="product"] .btn-cart-wrapper {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}

/* HIDE ANY ACTION BUTTONS */
.product-inner .product-actions,
.product-inner .product-footer,
.product-inner .product-button,
.product-inner .product-hover-button,
.product-inner .product-hover-action,
.product-inner .product-hover-actions,
.product-inner .product-action,
.product-inner .product-action-bottom,
.product-inner .product-action-vertical,
.product-inner .hover-buttons,
.product-inner .hover-button,
.product-inner .hover-action,
.product-inner:hover .product-action,
.product-inner:hover .product-actions,
.product-inner:hover .product-button,
.product-inner:hover .product-buttons,
.product-inner:hover .hover-buttons,
.product-inner:hover .hover-action,
.product-inner:hover .hover-actions,
.product-inner:hover .product-hover-action,
.product-inner:hover .product-hover-actions,
.products .product-inner .product-actions,
.products .product-inner .product-footer,
.products .product-inner .product-button,
.products .product-inner .product-hover-button,
.products .product-inner .product-hover-action,
.products .product-inner .product-hover-actions,
.products .product-inner .product-action,
.products .product-inner .product-action-bottom,
.products .product-inner .product-action-vertical,
.products .product-inner .hover-buttons,
.products .product-inner .hover-button,
.products .product-inner .hover-action,
.products .product-inner:hover .product-action,
.products .product-inner:hover .product-actions,
.products .product-inner:hover .product-button,
.products .product-inner:hover .product-buttons,
.products .product-inner:hover .hover-buttons,
.products .product-inner:hover .hover-action,
.products .product-inner:hover .hover-actions,
.products .product-inner:hover .product-hover-action,
.products .product-inner:hover .product-hover-actions,
*[class*="product"] .product-actions,
*[class*="product"] .product-footer,
*[class*="product"] .product-button,
*[class*="product"] .product-hover-button,
*[class*="product"] .product-hover-action,
*[class*="product"] .product-hover-actions,
*[class*="product"] .product-action,
*[class*="product"] .product-action-bottom,
*[class*="product"] .product-action-vertical,
*[class*="product"] .hover-buttons,
*[class*="product"] .hover-button,
*[class*="product"] .hover-action,
*[class*="product"]:hover .product-action,
*[class*="product"]:hover .product-actions,
*[class*="product"]:hover .product-button,
*[class*="product"]:hover .product-buttons,
*[class*="product"]:hover .hover-buttons,
*[class*="product"]:hover .hover-action,
*[class*="product"]:hover .hover-actions,
*[class*="product"]:hover .product-hover-action,
*[class*="product"]:hover .product-hover-actions {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}

/* HIDE QUANTITY SELECTOR */
.product-inner .quantity,
.product-inner .qty-input,
.product-inner .qty-wrapper,
.product-inner .qty-box,
.product-inner .quantity-input,
.product-inner .quantity-wrapper,
.product-inner .quantity-box,
.product-inner .product-quantity,
.product-inner .product-qty,
.products .product-inner .quantity,
.products .product-inner .qty-input,
.products .product-inner .qty-wrapper,
.products .product-inner .qty-box,
.products .product-inner .quantity-input,
.products .product-inner .quantity-wrapper,
.products .product-inner .quantity-box,
.products .product-inner .product-quantity,
.products .product-inner .product-qty,
*[class*="product"] .quantity,
*[class*="product"] .qty-input,
*[class*="product"] .qty-wrapper,
*[class*="product"] .qty-box,
*[class*="product"] .quantity-input,
*[class*="product"] .quantity-wrapper,
*[class*="product"] .quantity-box,
*[class*="product"] .product-quantity,
*[class*="product"] .product-qty {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}

/* HIDE ANY CART-RELATED ELEMENTS */
.product-inner .cart-form,
.product-inner form.cart-form,
.product-inner .sticky-atc-wrap,
.product-inner .cart-wrapper,
.product-inner .cart-box,
.product-inner .cart-container,
.product-inner .cart-buttons,
.product-inner .cart-button,
.product-inner .cart-action,
.products .product-inner .cart-form,
.products .product-inner form.cart-form,
.products .product-inner .sticky-atc-wrap,
.products .product-inner .cart-wrapper,
.products .product-inner .cart-box,
.products .product-inner .cart-container,
.products .product-inner .cart-buttons,
.products .product-inner .cart-button,
.products .product-inner .cart-action,
*[class*="product"] .cart-form,
*[class*="product"] form.cart-form,
*[class*="product"] .sticky-atc-wrap,
*[class*="product"] .cart-wrapper,
*[class*="product"] .cart-box,
*[class*="product"] .cart-container,
*[class*="product"] .cart-buttons,
*[class*="product"] .cart-button,
*[class*="product"] .cart-action {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}
