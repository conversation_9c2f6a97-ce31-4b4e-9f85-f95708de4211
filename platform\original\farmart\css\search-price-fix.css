/**
 * Extremely aggressive fix for doubled price display in search results
 */

/* Target all price elements in product cards */
.product-inner .product-price:not(:first-of-type),
.product-inner .bb-product-price:not(:first-of-type),
.product-inner .price:not(:first-of-type),
.product-inner .price-amount:not(:first-of-type),
.product-inner [class*="price"]:not(:first-of-type),
.product-inner .product-price ~ .product-price,
.product-inner .bb-product-price ~ .bb-product-price,
.product-inner .price ~ .price,
.product-inner .price-amount ~ .price-amount {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

/* Target price elements in the product info section */
.product-inner .product-info .product-price:nth-of-type(n+2),
.product-inner .product-info .bb-product-price:nth-of-type(n+2),
.product-inner .product-info .price:nth-of-type(n+2),
.product-inner .product-info .price-amount:nth-of-type(n+2) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

/* Target 3-column grid specifically */
.col-lg-4 .product-inner .product-price:nth-of-type(n+2),
.col-lg-4 .product-inner .bb-product-price:nth-of-type(n+2),
.col-lg-4 .product-inner .price:nth-of-type(n+2),
.col-lg-4 .product-inner .price-amount:nth-of-type(n+2),
.col-md-4 .product-inner .product-price:nth-of-type(n+2),
.col-md-4 .product-inner .bb-product-price:nth-of-type(n+2),
.col-md-4 .product-inner .price:nth-of-type(n+2),
.col-md-4 .product-inner .price-amount:nth-of-type(n+2),
.col-4 .product-inner .product-price:nth-of-type(n+2),
.col-4 .product-inner .bb-product-price:nth-of-type(n+2),
.col-4 .product-inner .price:nth-of-type(n+2),
.col-4 .product-inner .price-amount:nth-of-type(n+2) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

/* Target search results specifically */
body.search-page .product-inner .product-price:nth-of-type(n+2),
body.search-page .product-inner .bb-product-price:nth-of-type(n+2),
body.search-page .product-inner .price:nth-of-type(n+2),
body.search-page .product-inner .price-amount:nth-of-type(n+2) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

/* Target specific elements that might be causing the issue */
.product-inner .product-info > span:nth-of-type(n+2),
.product-inner .product-info > div:nth-of-type(n+2) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
}

/* Target specific elements by their content */
.product-inner .product-info > *:has(> [class*="price"]:nth-of-type(n+2)) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    z-index: -999 !important;
}
