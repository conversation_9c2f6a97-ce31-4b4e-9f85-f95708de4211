/* Fix for modal close button */
#product-quick-view-modal .btn-close {
    position: absolute !important;
    top: 20px !important;
    right: 20px !important;
    border: 1px solid #ccc !important;
    border-radius: 3px !important;
    z-index: 10001 !important; /* Higher z-index to ensure it's above other elements */
    opacity: 1 !important;
    cursor: pointer !important;
    background-color: white !important;
    padding: 10px !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) !important;
}

/* Make sure the modal has the correct z-index */
#product-quick-view-modal {
    z-index: 10000 !important;
}

/* Ensure the modal backdrop doesn't block the close button */
.modal-backdrop {
    z-index: 9999 !important;
}

/* Add a visible X to the button in case the default one isn't showing */
#product-quick-view-modal .btn-close::before,
#product-quick-view-modal .btn-close::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: #333;
    display: block;
}

#product-quick-view-modal .btn-close::before {
    transform: rotate(45deg);
}

#product-quick-view-modal .btn-close::after {
    transform: rotate(-45deg);
}
