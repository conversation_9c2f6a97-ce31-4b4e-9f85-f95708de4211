@media (max-width: 991px) {
    .sticky-atc-wrap {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 999;
        background: #fff;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        padding: 15px;
        display: none;
    }

    .sticky-atc-wrap .d-flex {
        display: flex !important;
    }

    .sticky-atc-wrap .align-items-center {
        align-items: center !important;
    }

    .sticky-atc-wrap .w-100 {
        width: 100% !important;
    }

    .sticky-atc-wrap .quantity {
        min-width: 120px;
        margin-right: 10px;
    }

    .sticky-atc-wrap .qty-box {
        display: flex;
        align-items: center;
        height: 40px;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
    }

    .sticky-atc-wrap .qty-box .svg-icon {
        width: 30px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        user-select: none;
    }

    .sticky-atc-wrap .qty-box input {
        width: 60px;
        height: 40px;
        text-align: center;
        border: none;
        padding: 0;
        -moz-appearance: textfield;
    }

    .sticky-atc-wrap .qty-box input::-webkit-outer-spin-button,
    .sticky-atc-wrap .qty-box input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .sticky-atc-wrap .button-group {
        display: flex;
        gap: 10px;
        flex: 1;
    }

    .sticky-atc-wrap .btn {
        height: 40px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        padding: 0 15px;
        border-radius: 4px;
        text-transform: none;
    }

    .sticky-atc-wrap .btn-add-to-cart {
        background: var(--primary-color);
        color: #fff;
        border: none;
    }

    .sticky-atc-wrap .btn-buy-now {
        background: #ff6b6b;
        color: #fff;
        border: none;
    }
    
    /* Product variations styles */
    .sticky-atc-wrap .pr_switch_wrap {
        margin-bottom: 15px;
    }

    .sticky-atc-wrap .attribute-swatches-wrapper {
        margin-bottom: 15px;
    }

    .sticky-atc-wrap .attribute-swatch-item {
        display: inline-flex;
        margin-right: 8px;
        margin-bottom: 8px;
    }

    .sticky-atc-wrap .attribute-swatch-item label {
        margin: 0;
        cursor: pointer;
    }

    .sticky-atc-wrap .attribute-name {
        font-weight: 500;
        margin-bottom: 8px;
        display: block;
        color: #333;
        font-size: 14px;
    }
}
