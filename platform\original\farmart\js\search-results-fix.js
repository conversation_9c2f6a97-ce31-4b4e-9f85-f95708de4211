document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the search results page
    if (window.location.href.includes('/search?q=')) {
        // Add a class to the body to target with CSS
        document.body.classList.add('page-search');

        // Remove gap between product image and price tag
        function removeGapBetweenImageAndPrice() {
            const productThumbnails = document.querySelectorAll('.product-inner .product-thumbnail');
            productThumbnails.forEach(function(thumbnail) {
                thumbnail.style.marginBottom = '0';
                thumbnail.style.paddingBottom = '0';

                const thumbnailLinks = thumbnail.querySelectorAll('a');
                thumbnailLinks.forEach(function(link) {
                    link.style.marginBottom = '0';
                    link.style.paddingBottom = '0';
                });

                const thumbnailImages = thumbnail.querySelectorAll('img');
                thumbnailImages.forEach(function(img) {
                    img.style.marginBottom = '0';
                    img.style.display = 'block';
                });
            });

            const productDetails = document.querySelectorAll('.product-inner .product-details');
            productDetails.forEach(function(details) {
                details.style.marginTop = '-5px';
                details.style.paddingTop = '0';
            });
        }

        // Hide view options section
        function hideViewOptions() {
            const viewOptions = document.querySelectorAll('.catalog-toolbar__view, .toolbar-view__icon, .catalog-header__right');
            viewOptions.forEach(function(element) {
                if (element) {
                    element.style.display = 'none';
                    element.style.visibility = 'hidden';
                    element.style.opacity = '0';
                    element.style.pointerEvents = 'none';
                    element.style.width = '0';
                    element.style.height = '0';
                }
            });
        }

        // Fix double price display in 3-column grid, except for flash sale products
        function fixDoublePriceDisplay() {
            // Hide price display for non-flash sale products
            const duplicatePrices = document.querySelectorAll('.product-inner .d-none.d-md-block:not(.flash-sale-price)');
            duplicatePrices.forEach(function(element) {
                if (element) {
                    element.style.display = 'none';
                    element.style.visibility = 'hidden';
                    element.style.opacity = '0';
                    element.style.height = '0';
                    element.style.width = '0';
                    element.style.margin = '0';
                    element.style.padding = '0';
                    element.style.overflow = 'hidden';
                    element.style.position = 'absolute';
                    element.style.pointerEvents = 'none';
                }
            });

            // Show price display for flash sale products
            const flashSalePrices = document.querySelectorAll('.product-inner .flash-sale-price');
            flashSalePrices.forEach(function(element) {
                if (element) {
                    element.style.display = 'block';
                    element.style.visibility = 'visible';
                    element.style.opacity = '1';
                    element.style.height = 'auto';
                    element.style.width = 'auto';
                    element.style.margin = '10px 0';
                    element.style.padding = '0';
                    element.style.overflow = 'visible';
                    element.style.position = 'relative';
                    element.style.pointerEvents = 'auto';
                }
            });
        }

        // Remove quantity selectors, add to cart buttons, and hover actions
        function removeCartElements() {
            // Remove quantity selectors
            const quantitySelectors = document.querySelectorAll('.quantity, .qty-input, .qty-wrapper, .qty-box, .quantity-input, .quantity-wrapper, .quantity-box, .product-quantity, .product-qty');
            quantitySelectors.forEach(function(element) {
                if (element) {
                    element.style.display = 'none';
                    element.style.opacity = '0';
                    element.style.visibility = 'hidden';
                    element.style.pointerEvents = 'none';
                }
            });

            // Remove add to cart buttons
            const cartButtons = document.querySelectorAll('.add-to-cart-button, .product-cart-wrap, .product-button, button[type="submit"], .cart-form, .add-to-cart, .add_to_cart, .btn-add-to-cart, .btn-add-cart, .btn-cart, .btn-add-to-cart-wrapper, .btn-add-cart-wrapper, .btn-cart-wrapper');
            cartButtons.forEach(function(element) {
                if (element) {
                    element.style.display = 'none';
                    element.style.opacity = '0';
                    element.style.visibility = 'hidden';
                    element.style.pointerEvents = 'none';
                }
            });

            // Remove product action buttons and hover elements
            const actionButtons = document.querySelectorAll('.product-actions, .product-footer, .product-button, .product-hover-button, .product-hover-action, .product-hover-actions, .product-action, .product-action-bottom, .product-action-vertical, .hover-buttons, .hover-button, .hover-action');
            actionButtons.forEach(function(element) {
                if (element) {
                    element.style.display = 'none';
                    element.style.opacity = '0';
                    element.style.visibility = 'hidden';
                    element.style.pointerEvents = 'none';
                }
            });

            // Disable hover effects by adding a class to all product items
            const productItems = document.querySelectorAll('.product-inner');
            productItems.forEach(function(element) {
                if (element) {
                    element.classList.add('no-hover-actions');
                }
            });

            // Add inline style to override any hover effects
            if (!document.getElementById('no-hover-style')) {
                const style = document.createElement('style');
                style.id = 'no-hover-style';
                style.textContent = `
                    .no-hover-actions:hover .product-action,
                    .no-hover-actions:hover .product-actions,
                    .no-hover-actions:hover .product-button,
                    .no-hover-actions:hover .product-buttons,
                    .no-hover-actions:hover .hover-buttons,
                    .no-hover-actions:hover .hover-action,
                    .no-hover-actions:hover .hover-actions,
                    .no-hover-actions:hover .product-hover-action,
                    .no-hover-actions:hover .product-hover-actions,
                    .page-search .product-inner:hover .product-action,
                    .page-search .product-inner:hover .product-actions,
                    .page-search .product-inner:hover .product-button,
                    .page-search .product-inner:hover .product-buttons,
                    .page-search .product-inner:hover .hover-buttons,
                    .page-search .product-inner:hover .hover-action,
                    .page-search .product-inner:hover .hover-actions,
                    .page-search .product-inner:hover .product-hover-action,
                    .page-search .product-inner:hover .product-hover-actions {
                        display: none !important;
                        opacity: 0 !important;
                        visibility: hidden !important;
                        pointer-events: none !important;
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Run immediately
        removeGapBetweenImageAndPrice();
        hideViewOptions();
        fixDoublePriceDisplay();
        removeCartElements();

        // Also run after a short delay to catch elements that might be added dynamically
        setTimeout(function() {
            removeGapBetweenImageAndPrice();
            hideViewOptions();
            fixDoublePriceDisplay();
            removeCartElements();
        }, 500);

        setTimeout(function() {
            removeGapBetweenImageAndPrice();
            hideViewOptions();
            fixDoublePriceDisplay();
            removeCartElements();
        }, 1000);

        setTimeout(function() {
            removeGapBetweenImageAndPrice();
            hideViewOptions();
            fixDoublePriceDisplay();
            removeCartElements();
        }, 2000);

        // Monitor for DOM changes and remove cart elements when new content is added
        const observer = new MutationObserver(function(mutations) {
            removeGapBetweenImageAndPrice();
            hideViewOptions();
            fixDoublePriceDisplay();
            removeCartElements();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
});
