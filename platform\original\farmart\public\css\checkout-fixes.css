/* Custom CSS to fix checkout page issues */

/* Hide the Price: section completely */
.checkout-page .small.d-flex.justify-content-between {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
}

/* Hide any element that contains "Price:" text */
.checkout-page .small,
.checkout-page small,
.checkout-page div,
.checkout-page p,
.checkout-page span {
    /* This will be handled by JavaScript */
}

/* Improve product image styling */
.checkout-page .checkout-product-img-wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.checkout-page .checkout-product-img-wrapper:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.checkout-page .checkout-product-img-wrapper img {
    transition: transform 0.3s ease;
}

.checkout-page .checkout-product-img-wrapper:hover img {
    transform: scale(1.05);
}

/* Improve quantity controls styling */
.checkout-page .ec-checkout-quantity {
    border-radius: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e0e0e0;
    overflow: hidden;
    transition: all 0.2s ease;
}

.checkout-page .ec-checkout-quantity:hover {
    border-color: #ff6633;
    box-shadow: 0 2px 8px rgba(255, 102, 51, 0.2);
}

.checkout-page .ec-checkout-quantity-control {
    color: #ff6633;
    background-color: transparent;
    transition: all 0.2s ease;
}

.checkout-page .ec-checkout-quantity-control:hover {
    background-color: #fff0eb;
}

.checkout-page .checkout-quantity {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #ff6633;
    color: white;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
