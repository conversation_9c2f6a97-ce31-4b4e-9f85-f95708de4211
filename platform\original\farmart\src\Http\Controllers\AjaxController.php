<?php

namespace Theme\Farmart\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Responses\BaseHttpResponse;
use <PERSON><PERSON>ble\Theme\Facades\Theme;
use Bo<PERSON>ble\Theme\Http\Controllers\PublicController;
use Illuminate\Http\Request;
use Theme\Farmart\Supports\Wishlist;

class AjaxController extends PublicController
{
    /**
     * Get more products for the "More to Love" section
     *
     * @param Request $request
     * @param BaseHttpResponse $response
     * @return BaseHttpResponse
     */
    public function getMoreToLoveProducts(Request $request, BaseHttpResponse $response)
    {
        $page = (int)$request->input('page', 1);
        $perPage = 30;
        $offset = ($page - 1) * $perPage;

        // Get the current product ID from the request (if available)
        $currentProductId = (int)$request->input('product_id', 0);

        $condition = [
            'ec_products.status' => \Botble\Base\Enums\BaseStatusEnum::PUBLISHED,
        ];

        // Exclude current product if ID is provided
        if ($currentProductId > 0) {
            $condition[] = ['ec_products.id', '!=', $currentProductId];
        }

        $products = get_products([
            'condition' => $condition,
            'take' => $perPage + 1, // Take one more to check if there are more
            'skip' => $offset,
            'with' => [
                'slugable',
                'variations',
                'productLabels',
                'variationAttributeSwatchesForProductList',
                'productCollections',
            ],
            'order_by' => ['created_at' => 'DESC'],
        ]);

        $hasMore = $products->count() > $perPage;

        // Remove the extra product if there are more
        if ($hasMore) {
            $products = $products->take($perPage);
        }

        $wishlistIds = Wishlist::getWishlistIds($products->pluck('id')->all());

        $data = [];
        foreach ($products as $product) {
            $data[] = '<div class="product-inner bg-white">' . Theme::partial('ecommerce.product-item-grid', compact('product', 'wishlistIds')) . '</div>';
        }

        return $response->setData([
            'data' => [
                'products' => $data,
                'has_more' => $hasMore,
            ]
        ]);
    }
}
