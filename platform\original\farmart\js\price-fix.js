/**
 * Extremely aggressive fix for doubled price display in 3-column grid
 */

(function() {
    // Function to fix doubled price display
    function fixDoubledPriceDisplay() {
        // APPROACH 1: Remove duplicate prices by direct DOM manipulation

        // Get all product cards
        const productCards = document.querySelectorAll('.product-inner');

        // Loop through each card
        productCards.forEach(function(card) {
            // APPROACH 1A: Get all price elements and keep only the first one
            const allPriceElements = Array.from(card.querySelectorAll('.product-price, .bb-product-price, .price, .price-amount, [class*="price"]'));

            // If there's more than one price element, hide all but the first one
            if (allPriceElements.length > 1) {
                // Keep track of which elements we've seen
                const seenElements = new Set();

                allPriceElements.forEach(function(element) {
                    // Get the text content to identify duplicate prices
                    const priceText = element.textContent.trim();

                    if (seenElements.has(priceText)) {
                        // This is a duplicate, remove it completely
                        if (element.parentNode) {
                            element.parentNode.removeChild(element);
                        }
                    } else {
                        // First time seeing this price, add it to the set
                        seenElements.add(priceText);
                    }
                });
            }

            // APPROACH 1B: Specifically target price elements in the product info section
            const productInfo = card.querySelector('.product-info');
            if (productInfo) {
                const infoPriceElements = Array.from(productInfo.querySelectorAll('.product-price, .bb-product-price, .price, .price-amount, [class*="price"]'));

                // If there's more than one price element, keep only the first one
                if (infoPriceElements.length > 1) {
                    // Keep the first one, remove the rest
                    for (let i = 1; i < infoPriceElements.length; i++) {
                        if (infoPriceElements[i].parentNode) {
                            infoPriceElements[i].parentNode.removeChild(infoPriceElements[i]);
                        }
                    }
                }
            }

            // APPROACH 1C: Target specific elements that might be causing the issue
            // Find all elements with the same price text and keep only the first one
            const priceTexts = {};
            card.querySelectorAll('[class*="price"], .amount').forEach(function(element) {
                const text = element.textContent.trim();
                if (text && text.includes('$')) {
                    if (!priceTexts[text]) {
                        priceTexts[text] = [element];
                    } else {
                        priceTexts[text].push(element);
                    }
                }
            });

            // For each price text, keep only the first element
            Object.values(priceTexts).forEach(function(elements) {
                if (elements.length > 1) {
                    for (let i = 1; i < elements.length; i++) {
                        elements[i].style.display = 'none';
                        elements[i].style.visibility = 'hidden';
                        elements[i].style.opacity = '0';
                        elements[i].style.position = 'absolute';
                        elements[i].style.left = '-9999px';
                    }
                }
            });
        });

        // APPROACH 2: Use CSS to hide duplicate prices
        // Create a style element if it doesn't exist
        let styleElement = document.getElementById('price-fix-style');
        if (!styleElement) {
            styleElement = document.createElement('style');
            styleElement.id = 'price-fix-style';
            document.head.appendChild(styleElement);
        }

        // Add CSS rules to hide duplicate prices
        styleElement.textContent = `
            /* Hide duplicate prices */
            .product-inner .product-price:not(:first-of-type),
            .product-inner .bb-product-price:not(:first-of-type),
            .product-inner .price:not(:first-of-type),
            .product-inner .price-amount:not(:first-of-type),
            .product-inner [class*="price"]:not(:first-of-type),
            .product-inner .product-price ~ .product-price,
            .product-inner .bb-product-price ~ .bb-product-price,
            .product-inner .price ~ .price,
            .product-inner .price-amount ~ .price-amount,
            .product-inner .product-info .product-price:nth-of-type(n+2),
            .product-inner .product-info .bb-product-price:nth-of-type(n+2),
            .product-inner .product-info .price:nth-of-type(n+2),
            .product-inner .product-info .price-amount:nth-of-type(n+2) {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                position: absolute !important;
                left: -9999px !important;
                z-index: -999 !important;
                max-height: 0 !important;
                overflow: hidden !important;
                clip: rect(0, 0, 0, 0) !important;
                margin: 0 !important;
                padding: 0 !important;
                border: 0 !important;
                width: 0 !important;
                height: 0 !important;
            }
        `;
    }

    // Run when the DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixDoubledPriceDisplay);
    } else {
        fixDoubledPriceDisplay();
    }

    // Also run on window load
    window.addEventListener('load', fixDoubledPriceDisplay);

    // Run periodically to catch any dynamically loaded content
    setInterval(fixDoubledPriceDisplay, 1000);

    // Set up a MutationObserver to watch for dynamically added content
    const observer = new MutationObserver(function(mutations) {
        fixDoubledPriceDisplay();
    });

    // Start observing the document
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
})();
